import React from 'react';
import { Plus } from 'lucide-react';
import { ProposalControlsProps } from '@/types/component/proposals/TypeProposal';

const ComponentProposalControls: React.FC<ProposalControlsProps> = ({ searchQuery, onSearch, onAddProposal }) => (
    <div className="p-4 rounded-md bg-white border border-gray-200">
        <div className="flex flex-row w-full gap-2 justify-between items-center">
            <div className="relative w-1/2">
                <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <input
                    type="text"
                    placeholder="Search quotes..."
                    className="pl-10 w-full py-2 border border-gray-300 rounded-lg"
                    value={searchQuery}
                    onChange={onSearch}
                />

            </div>
            <button
                onClick={() => onAddProposal()}
                className="ml-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer transition-colors flex items-center"
            >
                <Plus className="h-5 w-5 mr-2" />
                New Proposal
            </button>
        </div>
    </div>
);

export default ComponentProposalControls;
