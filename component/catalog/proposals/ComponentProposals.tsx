'use client';

import React, { useEffect, useState, useMemo } from 'react';

import { useRouter } from 'next/navigation';
import PagePagination from '@/component/pagination/ComponentPagePagination';
import { CustomerType, CustomerStatus, QuoteStatus } from '@/lib/graphql/types/generated/graphql';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import ProposalControls from './ComponentProposalControls';
import { ProposalWithBasicDetails } from '@/types/component/proposals/TypeProposal';
import ConstantProposalTableHeaders from '@/constants/ConstantsProposalTableHeaders';
import { useGetAllProposalsQuery, GetAllProposalsQuery } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';

// Status color mapping for quote status badges
const statusColorMap: { [key: string]: string } = {
    'CREATED': 'bg-gray-100 text-gray-800',
    'SENT': 'bg-yellow-100 text-yellow-800',
    'ACCEPTED': 'bg-green-100 text-green-800',
    'REJECTED': 'bg-red-100 text-red-800',
    'ACTIVATED': 'bg-blue-100 text-blue-800',
    'DELETED': 'bg-red-100 text-red-800',
};

// Helper function to map GraphQL data to ProposalWithBasicDetails
const mapQuoteToProposal = (quote: GetAllProposalsQuery['quotesGet'][0]): ProposalWithBasicDetails => {
    // Extract customer name based on customer type
    const customerName = quote.customer.__typename === 'CustomerBusiness'
        ? quote.customer.basicDetails.legalName
        : (quote.customer as any).basicDetails.contactDetails.name;

    // Use truncated quote ID as proposal number (will be replaced with actual proposal number later)
    const proposalNumber = quote.id.substring(0, 8).toUpperCase();

    // Format expiry date - show "Unbounded" if validTill is "MAX"
    const expiryDate = quote.date.validTill === 'MAX' ? 'Unbounded' : (quote.date.validTill || 'Not Set');

    // Calculate total value (using totalSellingPrice + totalTaxValue for business logic)
    const totalValue = quote.quoteTotalSellingPrice.value + (quote.quoteTotalTaxAmount?.value || 0);

    // Get sales executive from assignments array (take first assignment)
    const firstAssignment = quote.assignments && quote.assignments.length > 0 ? quote.assignments[0] : null;

    return {
        id: quote.id,
        proposalNumber,
        customerName,
        customerType: quote.customer.__typename === 'CustomerBusiness' ? CustomerType.Business : CustomerType.Individual,
        customerStatus: CustomerStatus.Active, // Default to Active as not provided in GraphQL
        salesExecutive: firstAssignment?.salesExecutive?.name || 'Not Assigned',
        salesExecutiveId: firstAssignment?.salesExecutive?.id || '',
        customerSuccessManager: firstAssignment?.customerSuccessManger?.name || 'Not Assigned',
        customerSuccessManagerId: firstAssignment?.customerSuccessManger?.id || '',
        totalValue,
        currency: quote.currency,
        status: quote.status,
        createdDate: quote.date.validFrom,
        expiryDate,
        lastModified: quote.date.validFrom, // Using validFrom as lastModified since not available in GraphQL
        customerAcceptedDate: quote.status === QuoteStatus.Accepted ? quote.date.validFrom : null
    };
};

const ComponentProposals: React.FC = () => {
    const router = useRouter();

    // Apollo GraphQL query for proposals
    const { data, loading, error: queryError, refetch } = useGetAllProposalsQuery({
        fetchPolicy: 'cache-and-network',
        errorPolicy: 'all'
    });

    // Refetch data when component mounts to ensure fresh data
    useEffect(() => {
        refetch();
    }, [refetch]);

    const [visibleHeaders, setVisibleHeaders] = useState(ConstantProposalTableHeaders);
    const [searchQuery, setSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [quotes, setQuotes] = useState<ProposalWithBasicDetails[]>([]);
    const [paginatedQuotes, setPaginatedQuotes] = useState<ProposalWithBasicDetails[]>([]);

    const recordsPerPage = 10;

    // Filter quotes based on search query
    const filteredQuotes = useMemo(() => {
        if (!searchQuery.trim()) return quotes;
        const query = searchQuery.toLowerCase().trim();
        return quotes.filter(quote =>
            Object.values(quote).some(value =>
                String(value).toLowerCase().includes(query)
            )
        );
    }, [searchQuery, quotes]);

    const totalPages = Math.ceil(filteredQuotes.length / recordsPerPage);

    useEffect(() => {
        const paginatedQuotes = filteredQuotes.slice(
            (currentPage - 1) * recordsPerPage,
            currentPage * recordsPerPage
        );
        setPaginatedQuotes(paginatedQuotes);
    }, [filteredQuotes, currentPage]);

    // Process GraphQL data when it changes
    useEffect(() => {
        setCurrentPage(1);

        if (data?.quotesGet) {
            const mappedQuotes = data.quotesGet.map(mapQuoteToProposal);
            setQuotes(mappedQuotes);
        }
    }, [data]);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handleRowClick = (quoteId: string) => {
        router.push(`/catalog/proposals/new?mode=edit&id=${quoteId}`);
    };

    const handleAddProposal = () => {
        router.push('/catalog/proposals/new');
    };

    // Format currency value
    const formatCurrency = (value: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(value);
    };

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <div className="space-y-6">
            {/* Header Section */}
            <div className="flex items-center justify-between">
                <div className="flex flex-col gap-1">
                    <h2 className="text-2xl font-bold text-gray-900">Proposals</h2>
                    <p className="text-sm text-gray-600">
                        Manage your sales quotes and proposals
                    </p>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">{quotes.length}</div>
                        <div>Total Quotes</div>
                    </div>
                    <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                            {quotes.filter(q => q.status === QuoteStatus.Accepted).length}
                        </div>
                        <div>Approved</div>
                    </div>
                    <div className="text-center">
                        <div className="text-lg font-semibold text-yellow-600">
                            {quotes.filter(q => q.status === QuoteStatus.Sent).length}
                        </div>
                        <div>Pending</div>
                    </div>
                </div>
            </div>

            <ProposalControls
                searchQuery={searchQuery}
                onSearch={handleSearch}
                onAddProposal={handleAddProposal}
            />

            {loading && (
                <div className="bg-white rounded-lg border border-gray-200">
                    <ComponentLoading
                        message="Loading quotes..."
                        className="min-h-[400px]"
                    />
                </div>
            )}

            {queryError && (
                <div className="flex flex-col items-center justify-center min-h-[400px]">
                    <ComponentNote isError>
                        {getUserFriendlyErrorMessage(queryError as ApolloError)}
                    </ComponentNote>
                </div>
            )}

            {!loading && !queryError && (
                <div className="flex flex-col">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                        <div className="overflow-x-auto">
                            <div className="min-w-full">
                                <div className="flex bg-gray-50 border-b border-gray-200">
                                    {visibleHeaders.map((header) => (
                                        <div key={header.label} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider flex-1 min-w-0">
                                            {header.label}
                                        </div>
                                    ))}
                                </div>
                                <div>
                                    {paginatedQuotes.length === 0 ? (
                                        <div className="px-4 py-12 text-center text-gray-500">
                                            <div className="text-lg font-medium mb-2">No quotes found</div>
                                            <div className="text-sm">
                                                {searchQuery ? 'Try adjusting your search criteria' : 'Create your first quote to get started'}
                                            </div>
                                        </div>
                                    ) : (
                                        paginatedQuotes.map((quote) => (
                                            <div
                                                key={quote.id}
                                                className="flex border-b border-gray-200 hover:bg-gray-50 cursor-pointer transition-all duration-200 hover:shadow-sm"
                                                onClick={() => handleRowClick(quote.id)}
                                                role="button"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter' || e.key === ' ') {
                                                        e.preventDefault();
                                                        handleRowClick(quote.id);
                                                    }
                                                }}
                                            >
                                                {visibleHeaders.map((header) => {
                                                    let cellValue: any = header.key ? quote[header.key as keyof ProposalWithBasicDetails] : '';

                                                    // Special formatting for specific fields
                                                    if (header.key === 'totalValue') {
                                                        cellValue = formatCurrency(quote.totalValue, quote.currency);
                                                    } else if (header.key === 'createdDate') {
                                                        cellValue = formatDate(quote.createdDate);
                                                    } else if (header.key === 'expiryDate') {
                                                        cellValue = formatDate(quote.expiryDate);
                                                    }

                                                    return (
                                                        <div key={header.label} className="my-4 mx-4 overflow-hidden text-ellipsis text-sm text-gray-700 flex-1 min-w-0">
                                                            {header.pill ? (
                                                                <span className={`px-3 py-1 rounded-full text-xs font-semibold overflow-hidden text-ellipsis whitespace-nowrap ${statusColorMap[cellValue as string] || 'bg-gray-100 text-gray-800'}`}>
                                                                    {cellValue}
                                                                </span>
                                                            ) : (
                                                                <span
                                                                    className={`overflow-hidden text-ellipsis whitespace-nowrap ${header.key === 'proposalNumber' || header.key === 'customerName' ? 'font-medium' : ''}`}
                                                                    title={String(cellValue)}
                                                                >
                                                                    {cellValue}
                                                                </span>
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {totalPages > 1 && (
                        <div className="flex justify-end items-center p-4">
                            <PagePagination
                                currentPage={currentPage}
                                totalPages={totalPages}
                                onPageChange={setCurrentPage}
                            />
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default ComponentProposals;