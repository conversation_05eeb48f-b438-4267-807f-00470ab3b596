'use client';

import React, { useState, useEffect } from 'react';
import { Control, UseFormRegister, useFieldArray, useWatch, useFormContext } from 'react-hook-form';
import { ProductFormData } from '@/types/component/catalog/TypeProduct';
import { Plus, Trash2, AlertCircle } from 'lucide-react';
import ComponentTooltip from '@/component/common/ComponentTooltip';

interface ProductInfoSectionProps {
    control: Control<ProductFormData>;
    register: UseFormRegister<ProductFormData>;
}

const MAX_DIMENSIONS = 3;

const ComponentProductInfoSection: React.FC<ProductInfoSectionProps> = ({ control, register }) => {
    const { fields, append, remove, update } = useFieldArray({
        control,
        name: "dimensions"
    });

    const [error, setError] = useState<string | null>(null);
    const { setValue, watch } = useFormContext<ProductFormData>();

    // Watch for changes in dimensions and other fields
    const dimensions = useWatch({
        control,
        name: "dimensions"
    });

    // Validate for duplicate keys
    useEffect(() => {
        if (!dimensions) return;

        const keys = dimensions
            .filter(d => d && d.key)
            .map(d => d.key.toLowerCase());

        const duplicates = keys.filter((key, index) => keys.indexOf(key) !== index);

        if (duplicates.length > 0) {
            setError(`Duplicate dimension key: ${duplicates[0]}`);
        } else {
            setError(null);
        }
    }, [dimensions]);

    const handleAddDimension = () => {
        if (fields.length >= MAX_DIMENSIONS) {
            setError(`Maximum ${MAX_DIMENSIONS} dimensions allowed`);
            return;
        }
        append({ key: '', value: '' });
    };

    const handleDimensionBlur = (index: number, field: 'key' | 'value', currentValue: string) => {
        const dimension = dimensions?.[index];
        if (dimension) {
            update(index, {
                ...dimension,
                [field]: currentValue.trim().toLowerCase().replace(/\s+/g, '_')
            });
        }
    };

    return (
        <div className="space-y-6">
            <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Product Name <span className="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="name"
                    {...register('name', { required: true })}
                    className="w-full text-sm p-2 border border-gray-300 rounded-md"
                    placeholder="Enter product name"
                />

                {/* Dimensions Section */}
                <div className="pt-2">
                    <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">Product Dimensions</span>
                            <ComponentTooltip
                                content="Dimensions help uniquely identify your product. Eg: Country: India, Session Type: Remote"
                                position="right"
                            >
                                <AlertCircle className="h-4 w-4 text-gray-400" />
                            </ComponentTooltip>
                        </div>
                        <button
                            type="button"
                            onClick={handleAddDimension}
                            disabled={fields.length >= MAX_DIMENSIONS}
                            className={`flex items-center text-sm transition-colors ${fields.length >= MAX_DIMENSIONS
                                ? 'text-gray-400 cursor-not-allowed'
                                : 'text-primary hover:text-primary-dark'}`}
                        >
                            <Plus className="h-4 w-4 mr-1" />
                            Add Dimension
                        </button>
                    </div>

                    <div className="space-y-2 border-l-2 border-gray-400">
                        {fields.map((field, index) => (
                            <div
                                key={field.id}
                                className="flex ml-1.5 items-center gap-2 group animate-slideIn"
                            >
                                <input
                                    type="text"
                                    {...register(`dimensions.${index}.key`)}
                                    onBlur={(e) => handleDimensionBlur(index, 'key', e.target.value)}
                                    placeholder="Dimension name (e.g., Size, Color)"
                                    className="flex-1 text-sm p-1 border-b-1 rounded-md text-gray-500 ml-2 focus:border-b-1"
                                />
                                <input
                                    type="text"
                                    {...register(`dimensions.${index}.value`)}
                                    onBlur={(e) => handleDimensionBlur(index, 'value', e.target.value)}
                                    placeholder="Value (e.g., Large, Red)"
                                    className="flex-1 text-sm p-1 border-b-1 rounded-md text-gray-500 ml-2 focus:border-b-1"
                                />
                                <button
                                    type="button"
                                    onClick={() => remove(index)}
                                    className="text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors p-1.5 rounded-md"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </button>
                            </div>
                        ))}
                        {error && (
                            <div className="ml-3 text-red-500 text-xs mt-4 flex items-center gap-1">
                                <AlertCircle className="h-4 w-4" />
                                {error}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <div>
                <label htmlFor="productCode" className="block text-sm font-medium text-gray-700">
                    Product Code <span className="text-red-500">*</span>
                    <span className="ml-1 text-xs text-gray-500">(Auto-generated from Product Name if empty)</span>
                </label>
                <input
                    type="text"
                    id="productCode"
                    {...register('productCode', { required: false })}
                    className="w-full text-sm p-2 border border-gray-300 rounded-md mt-1"
                    placeholder="Enter product code"
                />
            </div>

            <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                </label>
                <textarea
                    id="description"
                    rows={4}
                    {...register('description')}
                    className="w-full text-sm p-2 border border-gray-300 rounded-md mt-1"
                    placeholder="Enter product description"
                />
            </div>
        </div>
    );
};

export default ComponentProductInfoSection; 