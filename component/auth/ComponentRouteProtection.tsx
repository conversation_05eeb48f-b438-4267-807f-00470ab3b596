/**
 * Route Protection Component
 *
 * This component provides global route protection with company and user verification.
 * It wraps protected routes and automatically handles both company and user verification flows.
 */

'use client';

import React, { ReactNode, createContext, useContext } from 'react';
import { useAuth } from '@clerk/nextjs';
import { usePathname } from 'next/navigation';
import { useCompanyVerification, useRequiresCompanyVerification } from '@/lib/graphql/hooks/useCompanyVerification';
import { useCompanyUserVerification, useRequiresCompanyUserVerification } from '@/lib/graphql/hooks/useCompanyUserVerification';
import { Company } from '@/lib/graphql';
import ComponentLoading from '@/component/common/ComponentLoading';

/**
 * Company Context Interface
 *
 * Provides company and user verification state to child components
 */
interface CompanyContextValue {
  /** Whether the user has a valid company */
  hasCompany: boolean;
  /** Whether the company verification is currently loading */
  isLoading: boolean;
  /** The company data if available */
  company: Company | null;
  /** Function to retry company verification */
  retry: () => void;
  /** Whether the user is an active company user */
  isActiveUser: boolean;
  /** Whether the user is invited but not activated */
  isInvitedUser: boolean;
  /** Whether the user verification is loading */
  isUserVerificationLoading: boolean;
  /** The company user data if available */
  companyUser: any | null;
  /** Function to retry user verification */
  retryUserVerification: () => void;
}

/**
 * Company Context
 *
 * React context for sharing company and user verification state throughout the component tree
 */
const CompanyContext = createContext<CompanyContextValue | null>(null);

/**
 * Hook to access company context
 *
 * This hook provides access to both company and user verification states from any child component
 * wrapped by ComponentRouteProtection.
 *
 * @returns CompanyContextValue containing company and user verification states
 * @throws Error if used outside of ComponentRouteProtection
 *
 * @example
 * ```typescript
 * function MyComponent() {
 *   const { hasCompany, isLoading, company, isActiveUser } = useCompanyContext();
 *
 *   if (!hasCompany) {
 *     return <div>Please complete company setup</div>;
 *   }
 *
 *   if (!isActiveUser) {
 *     return <div>Please activate your account</div>;
 *   }
 *
 *   return <div>Welcome to {company?.name}</div>;
 * }
 * ```
 */
export function useCompanyContext(): CompanyContextValue {
  const context = useContext(CompanyContext);

  if (!context) {
    throw new Error('useCompanyContext must be used within ComponentRouteProtection');
  }

  return context;
}

interface RouteProtectionProps {
  children: ReactNode;
}

/**
 * Loading component displayed during verification
 */
const VerificationLoader: React.FC<{ message: string }> = ({ message }) => (
  <div className="min-h-screen bg-gradient-primary">
    <ComponentLoading
      message={message}
      className="min-h-screen"
    />
  </div>
);

/**
 * Error component displayed when verification fails
 */
interface VerificationErrorProps {
  error: string;
  onRetry: () => void;
  isCTASignOut?: boolean;
}

const VerificationError: React.FC<VerificationErrorProps> = ({ error, onRetry, isCTASignOut = false }) => (
  <div className="min-h-screen bg-gradient-primary flex items-center justify-center">
    <div className="text-center max-w-md mx-auto px-4">
      <div className="bg-red-100 rounded-full h-12 w-12 flex items-center justify-center mx-auto mb-4">
        <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">Verification Failed</h2>
      <p className="text-gray-600 mb-4">{error}</p>
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors"
      >
        {isCTASignOut ? 'Sign Out' : 'Try Again'}
      </button>
    </div>
  </div>
);

/**
 * Route Protection Wrapper Component
 *
 * This component wraps the application content and provides:
 * 1. Authentication state checking
 * 2. Company verification for protected routes
 * 3. User verification for protected routes
 * 4. Automatic redirects for users without companies or proper access
 * 5. Loading and error states during verification
 * 6. Company and user context for child components
 */
export const ComponentRouteProtection: React.FC<RouteProtectionProps> = ({ children }) => {
  const { isSignedIn, isLoaded, signOut } = useAuth();
  const pathname = usePathname();
  const requiresCompanyVerification = useRequiresCompanyVerification();
  const requiresUserVerification = useRequiresCompanyUserVerification();

  // Company verification state
  const {
    hasCompany,
    isLoading: isVerifyingCompany,
    error: companyVerificationError,
    company,
    retry: retryCompanyVerification
  } = useCompanyVerification();

  // User verification state
  const {
    isActiveUser,
    isInvitedUser,
    isLoading: isVerifyingUser,
    error: userVerificationError,
    companyUser,
    retry: retryUserVerification
  } = useCompanyUserVerification();

  // Create context value
  const companyContextValue: CompanyContextValue = {
    hasCompany,
    isLoading: isVerifyingCompany,
    company,
    retry: retryCompanyVerification,
    isActiveUser,
    isInvitedUser,
    isUserVerificationLoading: isVerifyingUser,
    companyUser,
    retryUserVerification
  };

  // Show loading while Clerk auth is initializing
  if (!isLoaded) {
    return <VerificationLoader message="Initializing authentication..." />;
  }

  // If user is not signed in, let Clerk middleware handle the redirect
  if (!isSignedIn) {
    return (
      <CompanyContext.Provider value={companyContextValue}>
        {children}
      </CompanyContext.Provider>
    );
  }

  // For routes that don't require any verification, render children directly
  if (!requiresCompanyVerification && !requiresUserVerification) {
    return (
      <CompanyContext.Provider value={companyContextValue}>
        {children}
      </CompanyContext.Provider>
    );
  }

  // Step 1: Company Verification
  // Always check company verification first if it's required or if user verification is required
  // (since user verification implicitly requires company verification)
  if (requiresCompanyVerification || requiresUserVerification) {
    // Show loading during company verification
    if (isVerifyingCompany) {
      return <VerificationLoader message="Checking company information..." />;
    }

    // Show error if company verification failed
    if (companyVerificationError && !companyVerificationError.includes('redirecting')) {
      return <VerificationError error={companyVerificationError} onRetry={() => {
        signOut();
      }}
        isCTASignOut={true} />;
    }

    // If user doesn't have a company and we're not on company creation route,
    // the useCompanyVerification hook will handle the redirect
    if (!hasCompany && pathname !== '/company/create') {
      return <VerificationLoader message="Redirecting to company setup..." />;
    }
  }

  // Step 2: User Verification
  // Only proceed to user verification if company verification passed
  if (requiresUserVerification && hasCompany) {
    // Show loading during user verification
    if (isVerifyingUser) {
      return <VerificationLoader message="Verifying user access..." />;
    }

    // Show error if user verification failed
    if (userVerificationError) {
      return <VerificationError error={userVerificationError} onRetry={() => {
        signOut();
      }}
        isCTASignOut={true} />;
    }

    // If user is invited but not activated, the useCompanyUserVerification hook will handle the redirect
    if (isInvitedUser && pathname !== '/company/user/activate') {
      return <VerificationLoader message="Redirecting to user activation..." />;
    }

    // If user is not active and we're not on allowed routes, show error
    if (!isActiveUser && !isInvitedUser &&
      !['/company/create', '/company/user/activate'].includes(pathname)) {
      return <VerificationError
        error="You don't have access to this company. Please contact your administrator."
        onRetry={() => {
          signOut();
        }}
        isCTASignOut={true}
      />;
    }
  }

  // All verifications passed
  return (
    <CompanyContext.Provider value={companyContextValue}>
      {children}
    </CompanyContext.Provider>
  );
};

/**
 * Higher-Order Component for route protection
 *
 * This HOC can be used to wrap individual pages that need company and user verification.
 * Components wrapped with this HOC will have access to the company context via useCompanyContext.
 */
export function withRouteProtection<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  const ProtectedComponent: React.FC<P> = (props) => {
    return (
      <ComponentRouteProtection>
        <Component {...props} />
      </ComponentRouteProtection>
    );
  };

  ProtectedComponent.displayName = `withRouteProtection(${Component.displayName || Component.name})`;

  return ProtectedComponent;
}
