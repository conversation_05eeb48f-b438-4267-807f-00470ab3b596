import React from 'react';
import { CustomersControlsProps } from '@/types/component/customers/TypeCustomers';
import { Search } from 'lucide-react';

const CustomersControls: React.FC<CustomersControlsProps> = ({ searchQuery, onSearch, onAddCustomer }) => (
    <div className="p-4 rounded-md bg-white border border-gray-200">
        <div className="flex flex-row justify-between items-center">
           <div className="relative flex-1 max-w-lg">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Search className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            type="text"
                            placeholder="Search customers..."
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            value={searchQuery}
                            onChange={onSearch}
                        />
                    </div>  
            <button
                onClick={onAddCustomer}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add New Customer
            </button>
        </div>
    </div>
);

export default CustomersControls; 