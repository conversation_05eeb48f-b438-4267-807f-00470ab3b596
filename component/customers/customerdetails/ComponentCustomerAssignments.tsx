import React from 'react';
import { Edit, Users, Mail } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useGetCustomerByIdQuery } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import ComponentLoading from '@/component/common/ComponentLoading';

interface CustomerAssignmentsProps {
    customerId: string;
}

const ComponentCustomerAssignments: React.FC<CustomerAssignmentsProps> = ({ customerId }) => {
    const router = useRouter();
    const { data: customerData, loading: isLoading, error } = useGetCustomerByIdQuery({
        variables: { id: customerId },
    });

    if (isLoading) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 w-full p-6">
                <ComponentLoading
                    message="Loading assignments..."
                    className="min-h-[200px]"
                />
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 w-full p-6">
                <div className="text-red-600">
                    {getUserFriendlyErrorMessage(error)}
                </div>
            </div>
        );
    }

    const customer = customerData?.getCustomer;
    if (!customer) return null;

    const assignments = customer.assignments[0] || {};
    const { accountManager, supportRepresentative } = assignments;

    const handleEditClick = () => {
        router.push(`/customers/new?mode=edit&id=${customerId}`);
    };

    const AssignmentCard = ({ title, user }: { title: string; user: { name: string; id: string; email: string } | null | undefined }) => (
        user && (
            <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <img
                        src={`https://ui-avatars.com/api/?name=${user.name.replace(' ', '+')}&background=3B5BA5&color=fff`}
                        alt={user.name}
                        className="w-6 h-6 rounded-full"
                    />
                </div>
                <div className="flex-1">
                    <p className="text-sm font-medium text-gray-700 mb-1">{title}</p>
                    <p className="text-sm text-gray-900 font-semibold">{user.name}</p>
                    <p className="text-sm text-gray-600">{user.email}</p>
                </div>
            </div>
        )
    );

    return (
        <div className="bg-white rounded-lg border border-gray-200 w-full">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Users className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Team Assignment</h3>
                        <p className="text-sm text-gray-600">Assigned team members</p>
                    </div>
                </div>
                <button
                    onClick={handleEditClick}
                    className="p-2 rounded-lg hover:bg-gray-100 text-gray-500 hover:text-primary transition-colors"
                >
                    <Edit className="w-5 h-5" />
                </button>
            </div>
            <div className="p-6">
                {!accountManager && !supportRepresentative ? (
                    <div className="text-center py-8">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <Users className="w-6 h-6 text-gray-400" />
                        </div>
                        <p className="text-gray-600">No team members assigned</p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        <AssignmentCard title="Account Manager" user={accountManager} />
                        <AssignmentCard title="Support Representative" user={supportRepresentative} />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ComponentCustomerAssignments; 