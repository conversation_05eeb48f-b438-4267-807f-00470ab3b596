import React from 'react';
import { Clock, ArrowRight, Activity, DollarSign, Calendar } from 'lucide-react';

interface Activity {
    date: string;
    description: string;
    amount: string;
}

interface CustomerActivitiesTimelineProps {
    activities: Activity[];
}

const ActivityItem = ({ activity, isLast }: { activity: Activity, isLast: boolean }) => (
    <div className="flex gap-6">
        {/* Timeline Graphic */}
        <div className="flex flex-col items-center">
            <div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Activity className="w-6 h-6 text-blue-600" />
            </div>
            {!isLast && <div className="w-0.5 h-full bg-gray-200 mt-4"></div>}
        </div>

        {/* Content Card */}
        <div className="flex-1 pb-8">
            <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-sm transition-all duration-200">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-2">{activity.description}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                                <Calendar className="w-4 h-4" />
                                <span>{new Date(activity.date).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                })}</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <DollarSign className="w-4 h-4 text-green-600" />
                                <span className="font-semibold text-green-600">{activity.amount}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
);

const ComponentCustomerActivitiesTimeline: React.FC<CustomerActivitiesTimelineProps> = ({ activities }) => (
    <div className="space-y-4">
        {activities.length === 0 ? (
            <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Activity className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No activities yet</h3>
                <p className="text-gray-600">Customer activities and interactions will appear here as they occur.</p>
            </div>
        ) : (
            <>
                <div className="relative">
                    {activities.map((act, idx) => (
                        <ActivityItem key={idx} activity={act} isLast={idx === activities.length - 1} />
                    ))}
                </div>
                {activities.length > 5 && (
                    <div className="text-center pt-4">
                        <button className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:text-primary-hover bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            View All Activities
                            <ArrowRight className="w-4 h-4" />
                        </button>
                    </div>
                )}
            </>
        )}
    </div>
);

export default ComponentCustomerActivitiesTimeline;
