import React from 'react';
import { Briefcase, ArrowR<PERSON>, Settings, DollarSign, Clock } from 'lucide-react';

interface Service {
    name: string;
    description: string;
    cost: string;
}

interface CustomerServicesProps {
    services: Service[];
}

const ServiceItem = ({ service }: { service: Service }) => (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-sm transition-all duration-200">
        <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Settings className="w-6 h-6 text-purple-600" />
                </div>
                <div className="flex-1">
                    <h4 className="font-bold text-gray-900 text-lg mb-2">{service.name}</h4>
                    <p className="text-gray-600 mb-3">{service.description}</p>
                    <div className="flex items-center gap-2">
                        <DollarSign className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-semibold text-green-600">{service.cost}</span>
                    </div>
                </div>
            </div>
        </div>

        {/* Service Actions */}
        <div className="flex items-center justify-end gap-2 mt-4 pt-4 border-t border-gray-100">
            <button className="flex items-center gap-2 px-3 py-2 text-gsm text-gray-600 hover:text-primary transition-colors">
                <Clock className="w-4 h-4" />
                Schedule
            </button>
            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors">
                <Briefcase className="w-4 h-4" />
                Book Service
            </button>
        </div>
    </div>
);

const ComponentCustomerServices: React.FC<CustomerServicesProps> = ({ services }) => (
    <div className="space-y-4">
        {services.length === 0 ? (
            <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Settings className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No services yet</h3>
                <p className="text-gray-600">Services will appear here once they are assigned to this customer.</p>
                <button className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors">
                    <Briefcase className="w-4 h-4" />
                    Add Service
                </button>
            </div>
        ) : (
            <>
                <div className="grid gap-4">
                    {services.map((service, idx) => (
                        <ServiceItem key={idx} service={service} />
                    ))}
                </div>
                {services.length > 5 && (
                    <div className="text-center pt-4">
                        <button className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:text-primary-hover bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            View All Services
                            <ArrowRight className="w-4 h-4" />
                        </button>
                    </div>
                )}
            </>
        )}
    </div>
);

export default ComponentCustomerServices;
