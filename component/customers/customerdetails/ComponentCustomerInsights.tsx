import React from 'react';
import { DollarSign, BarChart2, TrendingUp } from 'lucide-react';

interface CustomerInsightsProps {
    lifetimeValue: number;
    avgInvoice: number;
    totalVisits: number;
}

const InsightItem = ({ icon: Icon, label, value, color = 'blue' }: {
    icon: React.ElementType,
    label: string,
    value: React.ReactNode,
    color?: 'blue' | 'green' | 'purple' | 'orange'
}) => {
    const colorClasses = {
        blue: 'bg-blue-100 text-blue-600',
        green: 'bg-green-100 text-green-600',
        purple: 'bg-purple-100 text-purple-600',
        orange: 'bg-orange-100 text-orange-600'
    };

    return (
        <div className="p-6 bg-gray-50 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
                        <Icon className="w-6 h-6" />
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">{label}</p>
                        <p className="text-2xl font-bold text-gray-900">{value}</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

const ComponentCustomerInsights: React.FC<CustomerInsightsProps> = ({ lifetimeValue, avgInvoice, totalVisits }) => (
    <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-bold text-gray-900 mb-3">Customer Insights</h3>
            <p className="text-gray-600">Key metrics and performance indicators</p>
        </div>
        <div className="p-6 space-y-4">
            <InsightItem
                label="Lifetime Value"
                value={`$${lifetimeValue.toLocaleString()}`}
                icon={DollarSign}
                color="green"
            />
            <InsightItem
                label="Avg. Invoice Value"
                value={`$${avgInvoice.toLocaleString()}`}
                icon={BarChart2}
                color="blue"
            />
            <InsightItem
                label="Total Visits"
                value={totalVisits}
                icon={TrendingUp}
                color="purple"
            />
        </div>
    </div>
);

export default ComponentCustomerInsights;
