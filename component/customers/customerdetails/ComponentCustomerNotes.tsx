import React, { useState } from 'react';
import { Edit, MessageSquare, Tag, ChevronDown, ChevronUp } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useGetCustomerByIdQuery } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';

interface CustomerNotesProps {
    customerId: string;
}

const ComponentCustomerNotes: React.FC<CustomerNotesProps> = ({ customerId }) => {
    const router = useRouter();
    const [showAllNotes, setShowAllNotes] = useState(false);
    const { data: customerData, loading: isLoading, error } = useGetCustomerByIdQuery({
        variables: { id: customerId },
    });

    if (isLoading) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 w-full p-6">
                <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="space-y-3">
                        <div className="h-20 bg-gray-100 rounded"></div>
                        <div className="h-20 bg-gray-100 rounded"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 w-full p-6">
                <div className="text-red-600">
                    {getUserFriendlyErrorMessage(error)}
                </div>
            </div>
        );
    }

    const customer = customerData?.getCustomer;
    if (!customer) return null;

    const notes = customer.notes || [];
    const notesToShow = notes.slice().reverse();

    const handleEditClick = () => {
        router.push(`/customers/new?mode=edit&id=${customerId}`);
    };

    return (
        <div className="bg-white rounded-lg border border-gray-200 w-full">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <MessageSquare className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Notes</h3>
                        <p className="text-sm text-gray-600">Customer interaction history and notes</p>
                    </div>
                </div>
                <button
                    onClick={handleEditClick}
                    className="p-2 rounded-lg hover:bg-gray-100 text-gray-500 hover:text-primary transition-colors"
                >
                    <Edit className="w-5 h-5" />
                </button>
            </div>
            <div className="p-6">
                {notes.length === 0 ? (
                    <div className="text-center py-8">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <MessageSquare className="w-6 h-6 text-gray-400" />
                        </div>
                        <p className="text-gray-600">No notes available</p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {(showAllNotes ? notesToShow : notesToShow.slice(0, 3)).map((note, index) => note && (
                            <div key={note.id || index} className="bg-gray-50 rounded-lg p-4">
                                <p className="text-gray-700 mb-2">{note.content}</p>
                                {note.tags.length > 0 && (
                                    <div className="flex items-center gap-2 flex-wrap">
                                        {note.tags.map((tag, tagIndex) => (
                                            <span
                                                key={tagIndex}
                                                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded"
                                            >
                                                <Tag className="w-3 h-3" />
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                        {notesToShow.length > 3 && (
                            <div className="flex items-center justify-center pt-2">
                                <button
                                    type="button"
                                    onClick={() => setShowAllNotes(!showAllNotes)}
                                    className="flex items-center gap-2 text-sm text-primary hover:text-secondary font-medium transition-colors"
                                >
                                    {showAllNotes ? (
                                        <>
                                            <ChevronUp className="h-4 w-4" />
                                            Show Less
                                        </>
                                    ) : (
                                        <>
                                            <ChevronDown className="h-4 w-4" />
                                            Show More
                                        </>
                                    )}
                                </button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default ComponentCustomerNotes;