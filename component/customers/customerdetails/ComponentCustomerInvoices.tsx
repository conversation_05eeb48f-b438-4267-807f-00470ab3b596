import React from 'react';
import { FileText, Calendar, DollarSign, ArrowRight, Eye, Download } from 'lucide-react';

interface Invoice {
    id: string;
    date: string;
    amount: number;
    status: string;
}

interface CustomerInvoicesProps {
    invoices: Invoice[];
}

const statusColorMap: { [key: string]: { bg: string; text: string; } } = {
    'Paid': { bg: 'bg-green-100', text: 'text-green-800' },
    'Pending': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
};

const InvoiceItem = ({ invoice }: { invoice: Invoice }) => {
    const statusStyle = statusColorMap[invoice.status] || { bg: 'bg-gray-100', text: 'text-gray-800' };
    return (
        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-sm transition-all duration-200">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <div className={`w-12 h-12 ${statusStyle.bg} rounded-lg flex items-center justify-center`}>
                        <FileText className={`w-6 h-6 ${statusStyle.text}`} />
                    </div>
                    <div>
                        <p className="font-bold text-gray-900 text-lg">{invoice.id}</p>
                        <p className="text-sm text-gray-600 flex items-center gap-2 mt-1">
                            <Calendar className="w-4 h-4" />
                            {new Date(invoice.date).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                            })}
                        </p>
                    </div>
                </div>
                <div className="text-right">
                    <p className="font-bold text-2xl text-gray-900 mb-2">${invoice.amount.toLocaleString()}</p>
                    <div className="flex items-center gap-2">
                        <span className={`px-3 py-1 text-sm font-semibold rounded-full ${statusStyle.bg} ${statusStyle.text}`}>
                            {invoice.status}
                        </span>
                    </div>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-2 mt-4 pt-4 border-t border-gray-100">
                <button className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-primary transition-colors">
                    <Eye className="w-4 h-4" />
                    View
                </button>
                <button className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-primary transition-colors">
                    <Download className="w-4 h-4" />
                    Download
                </button>
            </div>
        </div>
    );
};

const ComponentCustomerInvoices: React.FC<CustomerInvoicesProps> = ({ invoices }) => (
    <div className="space-y-4">
        {invoices.length === 0 ? (
            <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No invoices yet</h3>
                <p className="text-gray-600">Invoices will appear here once they are created for this customer.</p>
            </div>
        ) : (
            <>
                <div className="grid gap-4">
                    {invoices.map(inv => (
                        <InvoiceItem key={inv.id} invoice={inv} />
                    ))}
                </div>
                {invoices.length > 5 && (
                    <div className="text-center pt-4">
                        <button className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:text-primary-hover bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            View All Invoices
                            <ArrowRight className="w-4 h-4" />
                        </button>
                    </div>
                )}
            </>
        )}
    </div>
);

export default ComponentCustomerInvoices;
