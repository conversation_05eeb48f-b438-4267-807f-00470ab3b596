import React from 'react';
import { CustomField } from '@/types/common/TypeCustomField';
import { CustomTagType } from '@/lib/graphql/types/generated/graphql';

interface ComponentCustomFieldProps {
    field: CustomField;
    value: string | number | boolean | Date | string[] | undefined;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>, index: number) => void;
    index: number;
    handleDeleteCustomField: (index: number) => void;
}

const renderInput = (
    field: CustomField,
    value: string | number | boolean | Date | string[] | undefined,
    onChange: (e: React.ChangeEvent<any>, index: number) => void,
    index: number
) => {
    switch (field.dataType) {
        case CustomTagType.String:
            return (
                <div className="w-full">
                    <input
                        type="text"
                        name={`customFields[${index}].value`}
                        value={value as string || field.defaultValue as string}
                        onChange={e => onChange(e, index)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                </div>
            );
        case CustomTagType.Numeric:
            return (
                <div className="w-full">
                    <input
                        type="number"
                        name={`customFields[${index}].value`}
                        value={value as number || field.defaultValue as number}
                        onChange={e => onChange(e, index)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                </div>
            );
        case CustomTagType.Boolean:
            return (
                <div className="w-full">
                    <label className="relative inline-flex items-center cursor-pointer w-full">
                        <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={Boolean(value) || field.defaultValue as boolean}
                            onChange={e => onChange(e, index)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:bg-primary transition-colors duration-200 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:after:translate-x-full peer-checked:after:border-white"></div>
                    </label>
                </div>
            );
        case CustomTagType.Date:
            return (
                <div className="w-full">
                    <input
                        type="date"
                        name={`customFields[${index}].value`}
                        value={value ? (typeof value === 'string' ? value : (value as Date).toISOString().split('T')[0]) : field.defaultValue as string}
                        onChange={e => onChange(e, index)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                </div>
            );
        case CustomTagType.Select:
            return (
                <div className="w-full">
                    <select
                        name={`customFields[${index}].value`}
                        value={value as string || field.defaultValue as string}
                        onChange={e => onChange(e, index)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                        {/* Options should be provided via field.options in a real scenario */}
                        <option value="">Select an option</option>
                        {(Array.isArray(field.defaultValue) ? field.defaultValue : []).map((option, i) => (
                            <option key={i} value={option}>{option}</option>
                        ))}
                    </select>
                </div>
            );
        case CustomTagType.String:
            return (
                <div className="w-full">
                    <select
                        name={`customFields[${index}].value`}
                        multiple
                        value={Array.isArray(value) ? value : field.defaultValue as string[]}
                        onChange={e => onChange(e, index)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                        {/* Options should be provided via field.options in a real scenario */}
                        {(Array.isArray(field.defaultValue) ? field.defaultValue : []).map((option, i) => (
                            <option key={i} value={option}>{option}</option>
                        ))}
                    </select>
                </div>
            );
        case CustomTagType.String:
            return (
                <div className="w-full">
                    <input
                        type="file"
                        name={`customFields[${index}].value`}
                        onChange={e => onChange(e, index)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                </div>
            );
        case CustomTagType.String:
            return (
                <div className="w-full">
                    <input
                        type="url"
                        name={`customFields[${index}].value`}
                        value={value as string || field.defaultValue as string}
                        onChange={e => onChange(e, index)}
                        placeholder="https://example.com"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                </div>
            );
        default:
            return null;
    }
};

const ComponentCustomField: React.FC<ComponentCustomFieldProps> = ({ field, value, onChange, index, handleDeleteCustomField }) => (
    <div>
        <div className="flex items-center justify-left gap-2 mb-2">
            <label className="text-sm font-medium text-gray">{field.label}</label>
            <button
                type="button"
                onClick={() => handleDeleteCustomField(index)}
                className="text-red-400 hover:text-red-500"
            >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
            </button>
        </div>
        {renderInput(field, value, onChange, index)}
    </div>
);

export default ComponentCustomField; 