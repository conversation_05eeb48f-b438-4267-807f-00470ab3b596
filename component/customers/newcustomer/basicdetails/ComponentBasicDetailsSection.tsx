import React from 'react';
import IndividualForm from './ComponentIndividualForm';
import BusinessForm from './ComponentBusinessForm';
import { CustomerType } from '@/lib/graphql/types/generated/graphql';

interface BasicDetailsSectionProps {
    customerTypeTab: CustomerType;
}

const BasicDetailsSection: React.FC<BasicDetailsSectionProps> = ({ customerTypeTab }) => {
    return (
        <div>
            {customerTypeTab === CustomerType.Individual ? (
                <IndividualForm />
            ) : (
                <BusinessForm />
            )}
        </div>
    );
};

export default BasicDetailsSection;