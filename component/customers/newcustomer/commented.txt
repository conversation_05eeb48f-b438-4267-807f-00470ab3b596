<ComponentAddCustomFieldModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onAdd={handleAddCustomField}
            />

        
    const handleCustomFieldValueChange = (value: any, index: number) => {
        const updatedFields = [...customFields];
        updatedFields[index] = { ...updatedFields[index], value };
        setValue('customFields', updatedFields);
    };

    const handleAddCustomField = (field: any) => {
        setValue('customFields', [...customFields, field]);
        setIsModalOpen(false);
    };

    const handleDeleteCustomField = (index: number) => {
        const updatedFields = customFields.filter((_: any, i: number) => i !== index);
        setValue('customFields', updatedFields);
    };

    <div className="md:col-span-2">
                <button
                    type="button"
                    onClick={() => setIsModalOpen(true)}
                    className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:text-primary-hover bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                >
                    <PlusCircle className="w-5 h-5" />
                    Add Custom Field
                </button>
            </div>