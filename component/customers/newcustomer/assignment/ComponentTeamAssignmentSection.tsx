import React from 'react';
import { useFormContext } from 'react-hook-form';
import { GetCompanyUsersQuery, CompanyUserStatus } from '@/lib/graphql/types/generated/graphql';
import { useGetCompanyUsersQuery } from '@/lib/graphql/types/generated/hooks';
import { useCompanyUserVerification } from '@/lib/graphql/hooks/useCompanyUserVerification';
import { User } from 'lucide-react';

interface SelectFieldProps {
    label: string;
    name: string;
    options: Array<{ value: string; label: string }>;
    isLoading?: boolean;
}

const SelectField: React.FC<SelectFieldProps> = ({ label, name, options, isLoading }) => {
    const { register } = useFormContext();

    return (
        <div>
            <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
                {label}
            </label>
            <select
                {...register(`assignments.${name}`)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
                disabled={isLoading}
            >
                <option value="">Select {label}</option>
                {options.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
            {isLoading && (
                <p className="mt-1 text-sm text-gray-500">Loading team members...</p>
            )}
        </div>
    );
};

const TeamAssignmentSection: React.FC = () => {
    const { data: companyUsersData, loading: isCompanyUsersLoading } = useGetCompanyUsersQuery();
    const { companyUser: currentUser } = useCompanyUserVerification();

    // Transform and filter company users
    const teamMembers = React.useMemo(() => {
        const users = companyUsersData?.getCompany?.users || [];

        return users
            .filter((user): user is NonNullable<typeof user> => {
                if (!user) return false;
                return user.status === CompanyUserStatus.Active;
            })
            .map(user => ({
                value: user.id,
                label: user.name
            }));
    }, [companyUsersData, currentUser]);

    return (
        <div className="space-y-4">
            <SelectField
                label="Account Manager"
                name="accountManager"
                options={teamMembers}
                isLoading={isCompanyUsersLoading}
            />
            <SelectField
                label="Support Representative"
                name="supportRepresentative"
                options={teamMembers}
                isLoading={isCompanyUsersLoading}
            />
        </div>
    );
};

export default TeamAssignmentSection; 