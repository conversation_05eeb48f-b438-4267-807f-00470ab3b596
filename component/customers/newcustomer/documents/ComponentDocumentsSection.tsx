import React, { useState } from 'react';
import { UploadCloud, File, X, Download } from 'lucide-react';

interface UploadedFile {
    id: string;
    name: string;
    size: string;
    type: string;
}

const DocumentsSection: React.FC = () => {
    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([
    ]);

    const removeFile = (id: string) => {
        setUploadedFiles(files => files.filter(file => file.id !== id));
    };

    return (
        <div className="space-y-4">
            {/* Upload Area */}
            <div className="relative">
                <input
                    type="file"
                    multiple
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept=".pdf,.docx,.png,.jpg,.jpeg"
                />
                <div className="flex justify-center w-full px-6 py-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary hover:bg-blue-50 transition-all duration-200 cursor-pointer">
                    <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                            <UploadCloud className="w-6 h-6 text-blue-600" />
                        </div>
                        <p className="text-sm text-gray-600 mb-1">
                            <span className="font-semibold text-primary hover:text-primary-hover">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                            PDF, DOCX, PNG, JPG (max. 10MB each)
                        </p>
                    </div>
                </div>
            </div>

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Uploaded Files</h4>
                    {uploadedFiles.map((file) => (
                        <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <File className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                                    <p className="text-xs text-gray-500">{file.size} • {file.type}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    type="button"
                                    className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                    title="Download"
                                >
                                    <Download className="h-4 w-4" />
                                </button>
                                <button
                                    type="button"
                                    onClick={() => removeFile(file.id)}
                                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                    title="Remove"
                                >
                                    <X className="h-4 w-4" />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default DocumentsSection;