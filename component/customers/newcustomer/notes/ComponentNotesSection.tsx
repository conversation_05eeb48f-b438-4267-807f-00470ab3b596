import React, { useState } from 'react';
import { ChevronDown, ChevronUp, MessageSquare, Notebook } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

const NotesSection: React.FC = () => {
    const { register, watch } = useFormContext();
    const notes = watch('notes');
    const [showAllNotes, setShowAllNotes] = useState(false);
    const notesToShow = notes.slice().reverse();
    return (
        <div className="space-y-4">
            <div className="space-y-3">
                <div className="relative">
                    <MessageSquare className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <textarea
                        {...register('newNotes.content')}
                        rows={4}
                        placeholder="Add any additional notes about the customer..."
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors "
                    />
                </div>
                {
                    showAllNotes ? notesToShow
                        .map((note: any, index: number) => (
                            <div key={index} className='flex items-center gap-2 bg-blue-100 p-2 rounded-lg'>
                                <Notebook className="h-5 w-5 text-blue-600" />
                                <p className=''>{note.content}</p>
                            </div>
                        )) : notesToShow.slice(0, 3)
                            .map((note: any, index: number) => (
                                <div key={index} className='flex items-center gap-2 bg-blue-100 p-2 rounded-lg'>
                                    <Notebook className="h-5 w-5 text-blue-600" />
                                    <p className=''>{note.content}</p>
                                </div>
                            ))}

                {notesToShow.length > 3 && (
                    <div className="flex items-center justify-center pt-2">
                        <button
                            type="button"
                            onClick={() => setShowAllNotes(!showAllNotes)}
                            className="flex items-center gap-2 text-sm text-primary hover:text-secondary font-medium transition-colors"
                        >
                            {showAllNotes ? (
                                <>
                                    <ChevronUp className="h-4 w-4" />
                                    Show Less
                                </>
                            ) : (
                                <>
                                    <ChevronDown className="h-4 w-4" />
                                    Show More
                                </>
                            )}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default NotesSection;