import { Headset, User } from "lucide-react";
import SecondaryNavbar, { SecondaryNavbarLayout } from "../common/ComponentSecondaryNavbar";
import { SecondaryNavItem } from "@/types/component/common/TypeSecondaryNavbar";

export interface ComponentCustomerLayoutProps {
    children: React.ReactNode;
}

const navItems: SecondaryNavItem[] = [
    {
        id: 'allcustomers',
        label: 'All Customers',
        icon: <User className="h-5 w-5" />,
        href: `/customers`,
    },
    {
        id: 'leads',
        label: 'Leads',
        icon: <Headset className="h-5 w-5 " />,
        href: `/customers/leads`,
    },
];

const ComponentCustomersLayout: React.FC<ComponentCustomerLayoutProps> = ({ children }) => {

    return (
        <div className="max-w-6xl mx-auto mb-10 mt-10">
            <div className='mb-10'>
                <SecondaryNavbarLayout
                    navbar={
                        <SecondaryNavbar
                            items={navItems}
                        />
                    }
                    content={children}
                />
            </div>
        </div>
    );
}

export default ComponentCustomersLayout;