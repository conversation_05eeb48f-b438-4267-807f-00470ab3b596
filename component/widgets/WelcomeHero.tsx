import React from 'react';
import AIInsightsPanel from '../ai/ComponentAIInsights';

interface WelcomeHeroProps {
    userName: string;
    companyName: string;
}

export default function WelcomeHero({ userName, companyName }: WelcomeHeroProps) {
    const currentHour = new Date().getHours();
    const greeting = currentHour < 12 ? 'Good morning' : currentHour < 18 ? 'Good afternoon' : 'Good evening';

    return (

        <div className="w-full flex flex-col items-center gap-4">
            <div>
                <h1 className="text-xl font-bold  mb-2">
                    {greeting}, {userName}! 👋
                </h1>
            </div>
            <div className="">
                <div className="mb-6 text-lg">
                    <span className="text-5xl font-semibold">The new way of managing your business is here!</span>
                    {/* <br /> <span className="text-lg mt-5">Ask AI anything about your business, from insights to tasks.</span> */}
                </div>
            </div>
        </div>
    );
} 