import React from 'react';

type CompanyStatus = 'created' | 'onboarding' | 'active' | 'suspended';

interface CompanyStatusCardProps {
    status: CompanyStatus;
    onStartOnboarding: () => void;
}

export default function CompanyStatusCard({ status, onStartOnboarding }: CompanyStatusCardProps) {
    const getStatusContent = () => {
        switch (status) {
            case 'created':
                return {
                    title: 'Welcome to Oplatz!',
                    description: 'Let\'s get your company set up. Complete the onboarding process to unlock all features.',
                    action: {
                        text: 'Start Onboarding',
                        onClick: onStartOnboarding
                    },
                    color: 'bg-blue-50 border-blue-200'
                };
            case 'onboarding':
                return {
                    title: 'Complete Your Setup',
                    description: 'You\'re almost there! Complete the remaining steps to activate your account.',
                    action: {
                        text: 'Continue Setup',
                        onClick: onStartOnboarding
                    },
                    color: 'bg-yellow-50 border-yellow-200'
                };
            case 'active':
                return {
                    title: 'Your Business is Ready!',
                    description: 'Your account is fully set up. Start managing your business with our powerful tools.',
                    action: {
                        text: 'View Dashboard',
                        onClick: () => window.location.href = '/dashboard'
                    },
                    color: 'bg-green-50 border-green-200'
                };
            case 'suspended':
                return {
                    title: 'Account Suspended',
                    description: 'Please contact support to reactivate your account.',
                    action: {
                        text: 'Contact Support',
                        onClick: () => window.location.href = '/support'
                    },
                    color: 'bg-red-50 border-red-200'
                };
        }
    };

    const content = getStatusContent();

    return (
        <div className={`rounded-lg border p-6 ${content.color}`}>
            <h3 className="text-xl font-semibold mb-2">{content.title}</h3>
            <p className="text-gray-600 mb-4">{content.description}</p>
            <button
                onClick={content.action.onClick}
                className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
                {content.action.text}
                <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>
    );
} 