import React, { useState } from 'react';
import { Tag, Info, Trash2, Plus, X, Edit3 } from 'lucide-react';

export type CustomTagInput = {
    id?: string;
    label: string;
    key: string;
    value: string;
    type: string;
    description?: string;
};

type Props = {
    value: CustomTagInput;
    onChange: (tag: CustomTagInput) => void;
    error?: string;
};

const ComponentCustomTagInput: React.FC<Props> = ({ value, onChange, error }) => {
    return (
        <div className="space-y-3 mt-4">
            <label className="block relative">
                <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                    <Tag className="h-4 w-4" />
                </span>
                <input
                    type="text"
                    placeholder="Label (eg: deal size)"
                    value={value.label}
                    onChange={e => onChange({ ...value, label: e.target.value })}
                    className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                />
            </label>

            <label className="block relative">
                <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                    <Tag className="h-4 w-4" />
                </span>
                <input
                    type="text"
                    placeholder="Value (eg: 100,000$)"
                    value={value.value}
                    onChange={e => onChange({ ...value, value: e.target.value })}
                    className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                />
            </label>


            {/* <label className="block relative">
                <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                    <Info className="h-4 w-4" />
                </span>
                <input
                    type="text"
                    placeholder="Description (optional)"
                    value={value.description || ''}
                    onChange={e => onChange({ ...value, description: e.target.value })}
                    className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                />
            </label> */}
            {error && <div className="text-red-500 text-xs mt-1">{error}</div>}
        </div>
    );
};

// New: Multi-tag management component

// View mode component for displaying tags as compact key:value pairs
type CustomTagViewProps = {
    tag: CustomTagInput;
    onDelete: () => void;
    onEdit: () => void;
};

const ComponentCustomTagView: React.FC<CustomTagViewProps> = ({ tag, onDelete, onEdit }) => {
    return (
        <div className="inline-flex items-center px-2 py-1 rounded-md bg-gray-50 border border-gray-200 text-sm group hover:shadow-sm transition-all duration-200">
            <span className="text-gray-600 font-medium">{tag.label}</span>
            <span className="mx-1.5 text-gray-400">:</span>
            <span className="text-gray-800 font-medium">{tag.value}</span>
            <div className="ml-2 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                    type="button"
                    onClick={onEdit}
                    className="text-gray-400 hover:text-blue-600 transition-colors p-0.5 rounded"
                    aria-label="Edit tag"
                >
                    <Edit3 className="h-3 w-3" />
                </button>
                <button
                    type="button"
                    onClick={onDelete}
                    className="text-gray-400 hover:text-red-600 transition-colors p-0.5 rounded"
                    aria-label="Delete tag"
                >
                    <X className="h-3 w-3" />
                </button>
            </div>
        </div>
    );
};

export type CustomTagsInputProps = {
    value?: CustomTagInput[];
    onTagsChange?: (tags: CustomTagInput[]) => void;
    error?: string;
    viewMode?: boolean; // New prop to control view/edit mode
};

const defaultTag: CustomTagInput = { label: '', key: '', value: '', type: '', description: '' };

export const ComponentCustomTagsInput: React.FC<CustomTagsInputProps> = ({ value = [], onTagsChange, error, viewMode = false }) => {
    // Use value prop directly instead of internal state for better form integration
    const tags = value;
    const [editingIndex, setEditingIndex] = useState<number | null>(null);

    const handleAddTag = () => {
        const newTags = [...tags, { ...defaultTag }];
        onTagsChange?.(newTags);
    };

    const handleRemoveTag = (idx: number) => {
        const newTags = tags.filter((_, i) => i !== idx);
        onTagsChange?.(newTags);
    };

    const handleTagChange = (idx: number, tag: CustomTagInput) => {
        const updatedTag = {
            ...tag,
            key: tag.label.trim().toLowerCase().replace(/\s+/g, '_'),
            type: 'STRING'
        }
        const newTags = [...tags];
        newTags[idx] = updatedTag;
        onTagsChange?.(newTags);
    };

    const handleEditTag = (index: number) => {
        setEditingIndex(index);
    };

    const handleSaveTag = () => {
        setEditingIndex(null);
    };

    return (
        <div>
            <div className="flex gap-2 mb-2 items-center">
                <h3 className="text-lg font-semibold text-gray-900">Custom Tags</h3>
                {!viewMode && (
                    <button type="button" onClick={handleAddTag} className="text-white hover:bg-opacity-90 text-xs bg-primary p-1 rounded-full cursor-pointer">
                        <Plus className='h-4 w-4' />
                    </button>
                )}
            </div>
            <div className="text-gray-500 text-sm mb-4">
                {viewMode ? "Custom tags for this item." : "Add custom tags to categorize or describe your product."}
            </div>

            {viewMode ? (
                // View Mode: Compact key:value pairs
                <div className="flex flex-wrap gap-2">
                    {tags.length > 0 ? (
                        tags.map((tag, idx) => (
                            editingIndex === idx ? (
                                // Edit mode for this specific tag
                                <div key={idx} className="bg-white border border-blue-200 rounded-lg p-3 shadow-sm min-w-[280px]">
                                    <div className="space-y-3">
                                        <div>
                                            <label className="block text-xs font-medium text-blue-600 mb-1">Label</label>
                                            <input
                                                type="text"
                                                placeholder="Label (eg: deal size)"
                                                value={tag.label}
                                                onChange={e => handleTagChange(idx, { ...tag, label: e.target.value })}
                                                className="w-full p-2 border border-blue-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-xs font-medium text-green-600 mb-1">Value</label>
                                            <input
                                                type="text"
                                                placeholder="Value (eg: 100,000$)"
                                                value={tag.value}
                                                onChange={e => handleTagChange(idx, { ...tag, value: e.target.value })}
                                                className="w-full p-2 border border-green-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                                            />
                                        </div>
                                        <div className="flex gap-2">
                                            <button
                                                type="button"
                                                onClick={handleSaveTag}
                                                className="flex-1 bg-blue-600 text-white text-xs py-1.5 px-3 rounded-md hover:bg-blue-700 transition-colors"
                                            >
                                                Save
                                            </button>
                                            <button
                                                type="button"
                                                onClick={() => handleRemoveTag(idx)}
                                                className="bg-red-500 text-white text-xs py-1.5 px-3 rounded-md hover:bg-red-600 transition-colors"
                                            >
                                                <X className="h-3 w-3" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                // View mode for this tag
                                <ComponentCustomTagView
                                    key={idx}
                                    tag={tag}
                                    onDelete={() => handleRemoveTag(idx)}
                                    onEdit={() => handleEditTag(idx)}
                                />
                            )
                        ))
                    ) : (
                        <div className="text-gray-400 text-sm italic">No custom tags added yet.</div>
                    )}
                </div>
            ) : (
                // Edit Mode: Card-based layout (original behavior)
                <div className={tags.length > 0 ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4" : ""}>
                    {tags.length > 0 ? (
                        tags.map((tag, idx) => (
                            <div key={idx} className="relative border border-gray-100 rounded-lg p-4 min-h-[120px]">
                                <ComponentCustomTagInput
                                    value={tag}
                                    onChange={t => handleTagChange(idx, t)}
                                />
                                <button
                                    type="button"
                                    onClick={() => handleRemoveTag(idx)}
                                    className="mt-2 bg-red-500/30 text-red-500 hover:bg-opacity-90 cursor-pointer rounded-md p-1 transition-colors w-full"
                                    aria-label="Remove Tag"
                                >
                                    <div className='flex items-center gap-2 justify-center'>
                                        <Trash2 className='h-3 w-3' /> Delete
                                    </div>
                                </button>
                            </div>
                        ))
                    ) : (
                        <div className="border border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-400 col-span-4">
                            No custom tags added yet.
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default ComponentCustomTagInput; 