import React, { useState } from 'react';
import { CustomField } from '@/types/common/TypeCustomField';
import { CustomTagType } from '@/lib/graphql/types/generated/graphql';

// Modal for adding a new custom field
interface ComponentAddCustomFieldModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAdd: (field: CustomField) => void;
}

const dataTypes: { label: string; value: CustomTagType }[] = [
    { label: 'String', value: CustomTagType.String },
    { label: 'Number', value: CustomTagType.Numeric },
    { label: 'Boolean', value: CustomTagType.Boolean },
    { label: 'Date', value: CustomTagType.Date },
    { label: 'Select', value: CustomTagType.Select },
    { label: 'Document', value: CustomTagType.String },
    { label: 'Link', value: CustomTagType.String },
];

const getDefaultValueForDataType = (dataType: CustomTagType): string | number | boolean | Date | string[] => {
    switch (dataType) {
        case CustomTagType.String:
            return '';
        case CustomTagType.Numeric:
            return 1255;
        case CustomTagType.Boolean:
            return false;
        case CustomTagType.Date:
            return new Date();
        case CustomTagType.Select:
            return ['Option 1'];
        case CustomTagType.String:
            return '';
        case CustomTagType.String:
            return '';
        default:
            return '';
    }
};

export const ComponentAddCustomFieldModal: React.FC<ComponentAddCustomFieldModalProps> = ({ isOpen, onClose, onAdd }) => {
    const [label, setLabel] = useState('');
    const [dataType, setDataType] = useState<CustomTagType>(CustomTagType.Numeric);
    const [defaultValue, setDefaultValue] = useState<any>(getDefaultValueForDataType(dataType));

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        let parsedValue: string | number | boolean | Date | string[] = defaultValue;
        if (dataType === CustomTagType.Numeric) parsedValue = Number(defaultValue);
        if (dataType === CustomTagType.Boolean) parsedValue = defaultValue === 'true';
        if (dataType === CustomTagType.Date) parsedValue = new Date(defaultValue);
        if (dataType === CustomTagType.Select) parsedValue = defaultValue.split(',');
        const newField = { label, dataType, defaultValue: parsedValue, key: label.toLowerCase().replace(/ /g, '_') };

        onAdd(newField);
        setLabel('');
        setDataType(CustomTagType.Numeric);
        setDefaultValue(getDefaultValueForDataType(CustomTagType.Numeric));
        onClose();
    };

    const renderDefaultValueInput = () => {
        switch (dataType) {
            case CustomTagType.String:
                return (
                    <input
                        type="text"
                        value={defaultValue}
                        onChange={e => setDefaultValue(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                );
            case CustomTagType.Numeric:
                return (
                    <input
                        type="number"
                        value={defaultValue}
                        onChange={e => setDefaultValue(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                );
            case CustomTagType.Boolean:
                return (
                    <select
                        value={defaultValue.toString()}
                        onChange={e => setDefaultValue(e.target.value === 'true')}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                        <option value="true">True</option>
                        <option value="false">False</option>
                    </select>
                );
            case CustomTagType.Date:
                return (
                    <input
                        type="date"
                        value={defaultValue instanceof Date ? defaultValue.toISOString().split('T')[0] : defaultValue}
                        onChange={e => setDefaultValue(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                );
            case CustomTagType.Select:
                return (
                    <input
                        type="text"
                        value={Array.isArray(defaultValue) ? defaultValue.join(',') : defaultValue}
                        onChange={e => setDefaultValue(e.target.value)}
                        placeholder="Option 1, Option 2, Option 3"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                );
            case CustomTagType.String:
                return (
                    <input
                        type="text"
                        value={defaultValue}
                        onChange={e => setDefaultValue(e.target.value)}
                        placeholder="Document URL"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                );
            case CustomTagType.String:
                return (
                    <input
                        type="text"
                        value={defaultValue}
                        onChange={e => setDefaultValue(e.target.value)}
                        placeholder="Link URL"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                );
            default:
                return null;
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center gap-4 bg-default-gray-20-opacity backdrop-blur-sm">
            <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl">
                <h3 className="text-lg font-semibold mb-4">Add Custom Field</h3>
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-1">Label</label>
                        <input
                            type="text"
                            value={label}
                            placeholder="Deal Size"
                            onChange={e => setLabel(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                            required
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">Data Type</label>
                        <select
                            value={dataType}
                            onChange={e => setDataType(e.target.value as CustomTagType)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                        >
                            {dataTypes.map(dt => (
                                <option key={dt.label} value={dt.value}>{dt.label}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">Default Value</label>
                        {renderDefaultValueInput()}
                    </div>
                    <div className="flex justify-end gap-2">
                        <button type="button" onClick={onClose} className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800">Cancel</button>
                        <button type="button" onClick={handleSubmit} className="px-4 py-2 rounded-lg bg-primary text-white">Add</button>
                    </div>
                </div>
            </div>
        </div>
    );
};