import React from 'react';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { useOkQuery } from '@/lib/graphql/types/generated/hooks';

interface HealthStatusProps {
    pollingInterval?: number;
    className?: string;
    showText?: boolean;
}

export const ComponentHealthStatus: React.FC<HealthStatusProps> = ({
    pollingInterval = 30000, // Default polling interval: 30 seconds
    className = '',
    showText = true,
}) => {
    const { data, loading, error } = useOkQuery({
        pollInterval: pollingInterval,
        fetchPolicy: 'network-only', // Don't cache health check results
        errorPolicy: 'all', // Show partial data even if there are errors
    });

    const getStatusColor = () => {
        if (loading) return 'bg-gray-400';
        if (error) return 'bg-secondary'; // Using secondary color (orange) for error
        return data?.ok ? 'bg-green-500' : 'bg-secondary';
    };

    const getStatusText = () => {
        if (loading) return 'Checking API status...';
        if (error) return getUserFriendlyErrorMessage(error);
        return data?.ok ? 'API is operational' : 'API is not responding';
    };

    const statusText = getStatusText();

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            <div
                className={`h-3 w-3 rounded-full ${getStatusColor()} transition-colors duration-300`}
                title={statusText}
                aria-label={`API Status: ${statusText}`}
                role="status"
            />
            {showText && (
                <span className="text-sm text-gray">{statusText}</span>
            )}
        </div>
    );
};

export default ComponentHealthStatus; 