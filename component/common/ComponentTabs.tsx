'use client';

import { LucideIcon } from 'lucide-react';
import React, { useState } from 'react';

interface Tab {
    label: string;
    content: React.ReactNode;
    icon?: React.ReactElement<LucideIcon>;
}

interface TabsProps {
    tabs: Tab[];
}

const Tabs: React.FC<TabsProps> = ({ tabs }) => {
    const [activeTab, setActiveTab] = useState(0);

    return (
        <div>
            <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                    {tabs.map((tab, index) => (
                        <button
                            key={tab.label}
                            onClick={() => setActiveTab(index)}
                            className={`${activeTab === index
                                ? 'border-primary text-primary'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none flex items-center`}
                        >
                            {tab.icon}
                            {tab.label}
                        </button>
                    ))}
                </nav>
            </div>
            <div className="mt-6">{tabs[activeTab].content}</div>
        </div>
    );
};

export default Tabs; 