'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { SecondaryNavbarProps, SecondaryNavbarLayoutProps } from '@/types/component/common/TypeSecondaryNavbar';
import ComponentTooltip from './ComponentTooltip';

const SecondaryNavbar: React.FC<SecondaryNavbarProps> = ({
    items,
    className = ''
}) => {
    const pathname = usePathname();

    return (
        <nav className={`secondary-navbar ${className}`}>
            {items.map((item) => {
                const isActive = pathname === item.href;

                return (
                    <ComponentTooltip
                        key={item.id}
                        content={item.label}
                        position="right"
                        delay={100}
                    >
                        <Link
                            href={item.href}
                            className={`secondary-navbar-item ${isActive ? 'active' : ''}`}
                            aria-label={item.label}
                        >
                            <span className="secondary-navbar-icon">
                                {item.icon}
                            </span>
                        </Link>
                    </ComponentTooltip>
                );
            })}
        </nav>
    );
};

export const SecondaryNavbarLayout: React.FC<SecondaryNavbarLayoutProps> = ({
    navbar,
    content,
    className = ''
}) => {
    return (
        <div className={`secondary-navbar-layout ${className}`}>
            {navbar}
            <div className="secondary-navbar-content">
                {content}
            </div>
        </div>
    );
};

export default SecondaryNavbar;
