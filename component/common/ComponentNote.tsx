import React from 'react';
import { Info } from 'lucide-react';

interface ComponentNoteProps {
    children: React.ReactNode;
    icon?: React.ReactNode;
    className?: string;
    isError?: boolean;
}

const ComponentNote: React.FC<ComponentNoteProps> = ({ children, icon, className, isError = false }) => {
    return (
        <div className={`flex items-start gap-3 rounded-lg bg-blue-50 border border-blue-200 p-4 text-blue-900 ${isError ? 'bg-red-50 border-red-200 text-red-900' : ''} ${className ?? ''}`.trim()}>
            <span className="mt-1">
                {icon ?? <Info className="w-5 h-5 text-blue-500" />}
            </span>
            <div className="flex-1 text-sm leading-relaxed">
                {children}
            </div>
        </div>
    );
};

export default ComponentNote; 