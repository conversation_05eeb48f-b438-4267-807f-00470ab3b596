import React from 'react';

interface LoadingProps {
    message?: string;
    className?: string;
}

const ComponentLoading: React.FC<LoadingProps> = ({
    message = 'Loading...',
    className = ''
}) => {
    return (
        <div className={`flex flex-col items-center justify-center min-h-[200px] ${className}`}>
            <div className="relative">
                {/* Static background circle */}
                <div className="w-12 h-12 rounded-full border-4 border-[#3B5BA5] opacity-20"></div>
                {/* Spinning foreground circle - only the top border visible */}
                <div className="w-12 h-12 rounded-full border-4 border-transparent border-t-[#3B5BA5] animate-spin absolute top-0 left-0"></div>
            </div>
            <p className="mt-4 text-gray">
                {message}
            </p>
        </div>
    );
};

export default ComponentLoading; 