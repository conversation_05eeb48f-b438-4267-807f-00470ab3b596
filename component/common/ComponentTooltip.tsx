'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { TooltipProps, TooltipPosition } from '@/types/component/common/TypeSecondaryNavbar';

const ComponentTooltip: React.FC<TooltipProps> = ({
    content,
    position = 'auto',
    delay = 300,
    children,
    className = '',
    disabled = false
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [actualPosition, setActualPosition] = useState<TooltipPosition>(position);
    const [showTimeout, setShowTimeout] = useState<NodeJS.Timeout | null>(null);
    const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
    
    const containerRef = useRef<HTMLDivElement>(null);
    const tooltipRef = useRef<HTMLDivElement>(null);

    // Calculate optimal position based on viewport boundaries
    const calculatePosition = useCallback((): TooltipPosition => {
        if (position !== 'auto' || !containerRef.current) {
            return position === 'auto' ? 'top' : position;
        }

        const rect = containerRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Estimated tooltip dimensions (will be refined when tooltip is rendered)
        const tooltipWidth = 120;
        const tooltipHeight = 40;
        const margin = 16;

        // Check space availability in each direction
        const spaceTop = rect.top;
        const spaceBottom = viewportHeight - rect.bottom;
        const spaceLeft = rect.left;
        const spaceRight = viewportWidth - rect.right;

        // Prioritize top/bottom positions for better UX
        if (spaceTop >= tooltipHeight + margin) {
            return 'top';
        } else if (spaceBottom >= tooltipHeight + margin) {
            return 'bottom';
        } else if (spaceRight >= tooltipWidth + margin) {
            return 'right';
        } else if (spaceLeft >= tooltipWidth + margin) {
            return 'left';
        }

        // Fallback to top if no ideal position
        return 'top';
    }, [position]);

    // Handle mouse enter with delay
    const handleMouseEnter = useCallback(() => {
        if (disabled) return;

        // Clear any existing hide timeout
        if (hideTimeout) {
            clearTimeout(hideTimeout);
            setHideTimeout(null);
        }

        // Set show timeout
        const timeout = setTimeout(() => {
            setActualPosition(calculatePosition());
            setIsVisible(true);
        }, delay);

        setShowTimeout(timeout);
    }, [disabled, delay, calculatePosition, hideTimeout]);

    // Handle mouse leave with delay
    const handleMouseLeave = useCallback(() => {
        if (disabled) return;

        // Clear any existing show timeout
        if (showTimeout) {
            clearTimeout(showTimeout);
            setShowTimeout(null);
        }

        // Set hide timeout for smoother UX
        const timeout = setTimeout(() => {
            setIsVisible(false);
        }, 100);

        setHideTimeout(timeout);
    }, [disabled, showTimeout]);

    // Handle focus events for keyboard accessibility
    const handleFocus = useCallback(() => {
        if (disabled) return;
        setActualPosition(calculatePosition());
        setIsVisible(true);
    }, [disabled, calculatePosition]);

    const handleBlur = useCallback(() => {
        if (disabled) return;
        setIsVisible(false);
    }, [disabled]);

    // Cleanup timeouts on unmount
    useEffect(() => {
        return () => {
            if (showTimeout) clearTimeout(showTimeout);
            if (hideTimeout) clearTimeout(hideTimeout);
        };
    }, [showTimeout, hideTimeout]);

    // Handle window resize to recalculate position
    useEffect(() => {
        const handleResize = () => {
            if (isVisible && position === 'auto') {
                setActualPosition(calculatePosition());
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [isVisible, position, calculatePosition]);

    if (!content || disabled) {
        return <>{children}</>;
    }

    return (
        <div
            ref={containerRef}
            className={`tooltip-container ${className}`}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onFocus={handleFocus}
            onBlur={handleBlur}
        >
            {children}
            {isVisible && (
                <div
                    ref={tooltipRef}
                    className={`tooltip position-${actualPosition} ${isVisible ? 'visible' : ''}`}
                    role="tooltip"
                    aria-hidden={!isVisible}
                >
                    {content}
                </div>
            )}
        </div>
    );
};

export default ComponentTooltip;
