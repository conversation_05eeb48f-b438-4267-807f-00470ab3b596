# Secondary Navbar Component

A reusable vertical navigation component for detailed navigation within main content areas.

## Overview

The Secondary Navbar component provides a clean, vertical navigation interface that "hugs" the main content area, allowing users to navigate between different sections of a single entity (like customer details) without requiring full page navigation.

## Components

### `ComponentSecondaryNavbar.tsx`
The main navigation component that renders vertical navigation items.

### `SecondaryNavbarLayout`
A layout component that positions the navbar alongside content in a responsive manner.

## Usage

### Basic Implementation

```tsx
import SecondaryNavbar, { SecondaryNavbarLayout } from '@/component/common/ComponentSecondaryNavbar';
import { SecondaryNavItem } from '@/types/component/common/TypeSecondaryNavbar';
import useSecondaryNavigation from '@/hooks/useSecondaryNavigation';

const MyDetailView = () => {
    const { activeSection, setActiveSection } = useSecondaryNavigation('overview');

    const navItems: SecondaryNavItem[] = [
        {
            id: 'overview',
            label: 'Overview',
            icon: <User className="h-5 w-5" />,
            content: <div>Overview content</div>,
        },
        {
            id: 'details',
            label: 'Details',
            icon: <FileText className="h-5 w-5" />,
            content: <div>Details content</div>,
        },
    ];

    const activeNavItem = navItems.find(item => item.id === activeSection) || navItems[0];

    return (
        <SecondaryNavbarLayout
            navbar={
                <SecondaryNavbar
                    items={navItems}
                    activeItemId={activeSection}
                    onItemChange={setActiveSection}
                />
            }
            content={activeNavItem.content}
        />
    );
};
```

## Features

- **Responsive Design**: Automatically switches to horizontal layout on mobile devices
- **Active State Management**: Visual indication of current section
- **Smooth Transitions**: Hover effects and transitions for better UX
- **Icon Support**: Uses Lucide React icons for visual cues
- **Reusable**: Can be easily adapted for different detail pages

## Styling

The component uses custom CSS classes defined in `app/styles/_secondary-navbar.css`:

- `.secondary-navbar`: Main navbar container
- `.secondary-navbar-item`: Individual navigation items
- `.secondary-navbar-layout`: Layout wrapper
- `.secondary-navbar-content`: Content area

## Responsive Behavior

- **Desktop**: Vertical sidebar layout (240px width)
- **Mobile**: Horizontal scrollable navigation at the top

## Integration Example

See `component/customers/customerdetails/ComponentCustomerDetailView.tsx` for a complete implementation example with the customer detail page.

## Customization

The component accepts optional `className` props for additional styling and can be easily extended with new navigation items by updating the `navItems` array.
