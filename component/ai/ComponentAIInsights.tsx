import React, { useState } from 'react';
import { Bot, Send, Users, FileText, Calculator, BarChart3, Receipt } from 'lucide-react';
import AIInsightsModal from './AIInsightsModal';
import { useModalState } from '@/hooks/useModalState';

const AIInsightsPanel: React.FC = () => {
    const [message, setMessage] = useState('');
    const { isOpen, openModal, closeModal } = useModalState();

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setMessage(e.target.value);
    };

    return (
        <>
            {/* <div className="fixed opacity-80 inset-x-0 bottom-0 z-[99] flex flex-col items-center pointer-events-none px-4"> */}
            <div className="flex flex-col justify-start">
                <div
                    className="w-full max-w-2xl flex bg-white/80 backdrop-blur-lg border border-gray-200/70 rounded-xl px-4 py-2 mb-6 pointer-events-auto transition-all duration-300"
                    style={{ pointerEvents: 'auto' }}
                >
                    <div className="flex items-center flex-1">
                        <span className=" text-gray-400">
                            <Bot className="w-5 h-5" />
                        </span>
                        <input
                            type="text"
                            value={message}
                            onChange={handleInputChange}
                            onFocus={openModal}
                            placeholder="How can I help you?"
                            className="flex-1 bg-transparent border-none outline-none text-base placeholder-gray-500 px-2 py-1"
                        />
                    </div>
                    <button
                        type="button"
                        disabled={!message.trim()}
                        className="ml-2 w-9 h-9 flex items-center justify-center rounded-full text-black hover:bg-gray-300 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all"
                        aria-label="Send message"
                        onClick={openModal}
                    >
                        <Send className="w-4 h-4" />
                    </button>
                </div>
            </div>
            <AIInsightsModal
                isOpen={isOpen}
                onClose={closeModal}
                initialMessage={message}
            />
        </>
    );
};

export default AIInsightsPanel; 