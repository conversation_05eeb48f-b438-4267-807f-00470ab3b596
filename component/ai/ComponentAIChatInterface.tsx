import React, { useState, useRef, useEffect } from 'react';
import { Bot, Send } from 'lucide-react';
import { useChatLogic } from '@/hooks/useChatLogic';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import ComponentSuggestionCards from './ComponentSuggestionCards';
import { ChatMessage as ChatMessageType } from '@/types/component/ai/TypeAIInsightsModal';

interface ComponentAIChatInterfaceProps {
    className?: string;
}

const ComponentAIChatInterface: React.FC<ComponentAIChatInterfaceProps> = ({ className = '' }) => {
    const { messages, isTyping, addUserMessage } = useChatLogic();
    const [inputValue, setInputValue] = useState('');
    const chatContainerRef = useRef<HTMLDivElement>(null);
    const [showSuggestions, setShowSuggestions] = useState(true);

    // Add initial AI greeting message
    useEffect(() => {
        if (messages.length === 0) {
            const initialMessage: ChatMessageType = {
                id: crypto.randomUUID(),
                sender: 'bot',
                text: 'What can I do for you today?',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            };
            // We need to directly set this in the chat logic
        }
    }, []);

    useEffect(() => {
        if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
    }, [messages, isTyping]);

    useEffect(() => {
        // Hide suggestions after first user message
        if (messages.some(msg => msg.sender === 'user')) {
            setShowSuggestions(false);
        }
    }, [messages]);

    const handleSend = () => {
        if (inputValue.trim()) {
            addUserMessage(inputValue);
            setInputValue('');
            setShowSuggestions(false);
        }
    };

    const handleSuggestionClick = (action: string) => {
        addUserMessage(action);
        setShowSuggestions(false);
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    return (
        <div className={`flex flex-col h-full bg-white ${className}`}>
            {/* Chat Header */}
            {/* <div className="flex-shrink-0 p-6 border-b border-gray-100">
                <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
                        <Bot className="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
                        <p className="text-sm text-gray-500">Here to help with your business</p>
                    </div>
                </div>
            </div> */}

            {/* Chat Messages */}
            <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-6 space-y-4 scroll-smooth">
                {/* Initial greeting if no messages */}
                {messages.length === 0 && (
                    <div className="flex items-start gap-3 animate-in fade-in slide-in-from-bottom-4 duration-700">
                        <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0 shadow-sm">
                            <Bot className="w-5 h-5 text-white" />
                        </div>
                        <div className="bg-gray-50 rounded-2xl rounded-bl-none px-4 py-3 max-w-[85%] shadow-sm border border-gray-100">
                            <p className="text-gray-800 font-medium">What can I do for you today?</p>
                            <div className="text-xs text-gray-400 mt-1">
                                {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </div>
                        </div>
                    </div>
                )}

                {/* Suggestion Cards */}
                {showSuggestions && messages.length === 0 && (
                    <div className="ml-11 animate-in fade-in slide-in-from-bottom-4 duration-700 delay-300">
                        <ComponentSuggestionCards onCardClick={handleSuggestionClick} />
                    </div>
                )}

                {/* Chat Messages */}
                {messages.map((msg, index) => (
                    <div key={msg.id} className="animate-in fade-in slide-in-from-bottom-2 duration-500">
                        <ChatMessage message={msg} />
                    </div>
                ))}

                {/* Typing Indicator */}
                {isTyping && (
                    <div className="animate-in fade-in slide-in-from-bottom-2 duration-300">
                        <TypingIndicator />
                    </div>
                )}
            </div>

            {/* Chat Input */}
            <div className="flex-shrink-0 p-6 border-t border-gray-100">
                <div className="flex items-end space-x-3">
                    <div className="flex-1 relative">
                        <input
                            type="text"
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder="Type your message..."
                            className="w-full rounded-xl border border-gray-200 px-4 py-3 pr-12 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary text-sm"
                        />
                        <button
                            onClick={handleSend}
                            disabled={!inputValue.trim() || isTyping}
                            className={`absolute right-2 bottom-2 flex items-center justify-center rounded-lg bg-secondary text-white hover:bg-secondary/90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all ${
                                isTyping ? 'w-16 h-8 px-2' : 'w-8 h-8'
                            }`}
                            aria-label={isTyping ? "AI is responding" : "Send message"}
                        >
                            {isTyping ? (
                                <span className="text-xs font-medium">Submit</span>
                            ) : (
                                <Send className="w-4 h-4" />
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentAIChatInterface;
