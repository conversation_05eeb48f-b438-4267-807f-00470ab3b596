import React from 'react';
import { Users, FileText, Calculator, BarChart3, Receipt } from 'lucide-react';

interface SuggestionCard {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
    action: string;
}

interface SuggestionCardsProps {
    onCardClick: (action: string) => void;
}

const suggestionCards: SuggestionCard[] = [
    {
        id: 'create-customer',
        title: 'Create a customer',
        description: 'Add a new customer to your database',
        icon: <Users className="w-5 h-5" />,
        action: 'Create a new customer'
    },
    {
        id: 'create-proposal',
        title: 'Create a proposal',
        description: 'Generate a new business proposal',
        icon: <FileText className="w-5 h-5" />,
        action: 'Create a new proposal'
    },
    {
        id: 'generate-quote',
        title: 'Generate a quote',
        description: 'Create a quote for services',
        icon: <Calculator className="w-5 h-5" />,
        action: 'Generate a quote'
    },
    {
        id: 'view-insights',
        title: 'View customer insights',
        description: 'Analyze customer data and trends',
        icon: <BarChart3 className="w-5 h-5" />,
        action: 'Show me customer insights'
    },
    {
        id: 'manage-accounting',
        title: 'Manage accounting records',
        description: 'Review financial records and reports',
        icon: <Receipt className="w-5 h-5" />,
        action: 'Help me manage accounting records'
    }
];

const ComponentSuggestionCards: React.FC<SuggestionCardsProps> = ({ onCardClick }) => {
    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-4 animate-in fade-in duration-500">
            {suggestionCards.map((card, index) => (
                <button
                    key={card.id}
                    onClick={() => onCardClick(card.action)}
                    className="group p-4 bg-white border border-gray-200 rounded-xl hover:shadow-lg hover:border-secondary/30 hover:-translate-y-1 transition-all duration-300 text-left transform"
                    style={{ animationDelay: `${index * 100}ms` }}
                >
                    <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 p-2 bg-secondary/10 rounded-lg text-secondary group-hover:bg-secondary group-hover:text-white group-hover:scale-110 transition-all duration-300">
                            {card.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="text-sm font-semibold text-gray-900 group-hover:text-secondary transition-colors duration-300">
                                {card.title}
                            </h3>
                            <p className="text-xs text-gray-500 mt-1 line-clamp-2 group-hover:text-gray-600 transition-colors duration-300">
                                {card.description}
                            </p>
                        </div>
                    </div>
                </button>
            ))}
        </div>
    );
};

export default ComponentSuggestionCards;
