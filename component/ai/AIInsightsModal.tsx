import React, { useState, useRef, useEffect } from 'react';
import { Bot, Send, X } from 'lucide-react';
import { useChatLogic } from '@/hooks/useChatLogic';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import { AIInsightsModalProps } from '@/types/component/ai/TypeAIInsightsModal';

const AIInsightsModal: React.FC<AIInsightsModalProps> = ({ isOpen, onClose, initialMessage }) => {
    const { messages, isTyping, addUserMessage } = useChatLogic(initialMessage);
    const [inputValue, setInputValue] = useState('');
    const chatContainerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [isOpen]);

    useEffect(() => {
        if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
    }, [messages, isTyping]);

    const handleSend = () => {
        if (inputValue.trim()) {
            addUserMessage(inputValue);
            setInputValue('');
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-[100] flex items-center justify-center px-4 md:px-8  bg-default-gray-20-opacity backdrop-blur-xl ">
            <div className="bg-white backdrop-blur-xl border border-gray-200/20 rounded-2xl shadow-2xl max-w-7xl h-full md:w-[70vw] md:h-[70vh] flex flex-col transition-all duration-300 ease-in-out">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200/20">
                    <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <Bot className="w-5 h-5" /> Assistant
                    </h2>
                    <button onClick={onClose} className="p-1 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-700">
                        <X size={20} />
                    </button>
                </div>

                {/* Chat content */}
                <div ref={chatContainerRef} className="flex-1 px-6 py-4 overflow-y-auto">
                    {messages.map(msg => (
                        <ChatMessage key={msg.id} message={msg} />
                    ))}
                    {isTyping && <TypingIndicator />}
                </div>

                {/* Input area */}
                <div className="p-4 border-t border-gray-200/20 ">
                    <div className="flex items-center bg-gray-100/50 rounded-full">
                        <input
                            type="text"
                            value={inputValue}
                            onChange={e => setInputValue(e.target.value)}
                            onKeyPress={e => e.key === 'Enter' && handleSend()}
                            placeholder="How can I help you?"
                            className="flex-1 bg-transparent border-none outline-none text-base placeholder-gray-500 px-5 py-3 text-gray-800"
                            autoFocus
                        />
                        <button
                            onClick={handleSend}
                            disabled={!inputValue.trim() || isTyping}
                            className="m-1.5 w-10 h-10 flex items-center justify-center rounded-full bg-primary text-white hover:bg-primary-hover disabled:bg-gray-500 disabled:cursor-not-allowed transition-all"
                            aria-label="Send message"
                        >
                            <Send className="w-5 h-5" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AIInsightsModal; 