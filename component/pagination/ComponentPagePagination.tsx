import React from 'react';
import { PagePaginationProps } from '@/types/common/TypePagePagination';

const MAX_VISIBLE_PAGES = 5;

const PagePagination: React.FC<PagePaginationProps> = ({ currentPage, totalPages, onPageChange }) => {
    // Calculate the range of page numbers to display
    let startPage = Math.max(1, currentPage - Math.floor(MAX_VISIBLE_PAGES / 2));
    let endPage = startPage + MAX_VISIBLE_PAGES - 1;
    if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(1, endPage - MAX_VISIBLE_PAGES + 1);
    }
    const pageNumbers = [];
    for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
    }

    return (
        <div className="flex items-center gap-2">
            <button
                className="px-3 py-1 rounded bg-gray-200 text-gray-600 disabled:opacity-50"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
            >
                Previous
            </button>
            {pageNumbers.map((page) => (
                <button
                    key={page}
                    className={`px-3 py-1 rounded border text-sm font-medium transition-colors ${page === currentPage
                        ? 'bg-primary text-white border-primary'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100'
                        }`}
                    onClick={() => onPageChange(page)}
                    disabled={page === currentPage}
                >
                    {page}
                </button>
            ))}
            <button
                className="px-3 py-1 rounded bg-primary text-white disabled:opacity-50"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
            >
                Next
            </button>
        </div>
    );
};

export default PagePagination; 