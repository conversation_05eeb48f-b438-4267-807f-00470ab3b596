'use client';

import React, { useState, useEffect, use } from 'react';
import { Users, Plus, Trash2, Mail, User, Shield, Users2 } from 'lucide-react';
import { useUser } from '@clerk/nextjs';
import { CompanyUserRole, CompanyUserStatus, CompanyUser, CompanyUserDeactivateMutation } from '@/lib/graphql/types/generated/graphql';
import { useCompanyContext } from '@/component/auth/ComponentRouteProtection';
import { useCompanyUserCreateMutation, useCompanyUserInviteMutation, useGetCompanyUsersQuery, useCompanyUserDeactivateMutation, CompanyUserCreateMutation, CompanyUserInviteMutation } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import ComponentLoading from '@/component/common/ComponentLoading';

type UserRole = 'ADMIN' | 'MANAGER' | 'USER';

const ComponentCompanyUserSetting: React.FC = () => {
    // fetchers
    const { company } = useCompanyContext();
    const { user: currentUser } = useUser();
    const { data: companyUsersData, loading: isCompanyUsersLoading, error: companyUsersError, refetch: refetchCompanyUsers } = useGetCompanyUsersQuery();

    // state
    const [newAdminUser, setNewAdminUser] = useState({ name: '', email: '', role: 'ADMIN' as UserRole });
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [companyUsers, setCompanyUsers] = useState<CompanyUser[]>([]);

    useEffect(() => {
        if (isCompanyUsersLoading) {
            setIsLoading(true);
        } else {
            if (companyUsersError) {
                setError(companyUsersError.message);
            }
            if (companyUsersData?.getCompany?.users) {
                setCompanyUsers(companyUsersData.getCompany.users as CompanyUser[]);
            }
            setIsLoading(false);
        }
    }, [companyUsersData, companyUsersError]);

    useEffect(() => {
        if (!isLoading && !error) {
            refetchCompanyUsers();
        }

    }, [isLoading, error])


    // GraphQL mutations
    const [companyUserCreate] = useCompanyUserCreateMutation({
        onCompleted: (result: CompanyUserCreateMutation) => {
            if (result?.companyUserCreate) {
                setError(null);
                setIsLoading(false);
            }
        },
        onError: (apolloError) => {
            const errorMessage = getUserFriendlyErrorMessage(apolloError);
            setError(errorMessage);
            setIsLoading(false);
        }
    });

    const [companyUserInvite] = useCompanyUserInviteMutation({
        onCompleted: (data: CompanyUserInviteMutation) => {
            if (data?.companyUserInvite) {
                setError(null);
                setIsLoading(false);
            }
        },
        onError: (apolloError) => {
            const errorMessage = getUserFriendlyErrorMessage(apolloError);
            setError(errorMessage);
            setIsLoading(false);

        }
    });

    const [companyUserDeactivate] = useCompanyUserDeactivateMutation({
        onCompleted: (data: CompanyUserDeactivateMutation) => {
            if (data?.companyUserDeactivate) {
                setError(null);
                setIsLoading(false);
            }
        },
        onError: (apolloError) => {
            const errorMessage = getUserFriendlyErrorMessage(apolloError);
            setError(errorMessage);
            setIsLoading(false);
        }
    });

    // TODO: Add Manager and User roles from graph types
    const roles = [
        { value: 'ADMIN', label: 'Admin', description: 'Full access to all features and settings' },
        //{ value: 'MANAGER', label: 'Manager', description: 'Manage team and view reports' },
        //{ value: 'USER', label: 'User', description: 'Basic access to assigned features' }
    ];

    // Map local role types to GraphQL enum types
    const mapRoleToGraphQL = (role: UserRole): CompanyUserRole => {
        switch (role) {
            case 'ADMIN':
                return CompanyUserRole.Admin;
            default:
                return CompanyUserRole.Admin;
        }
    };

    const addAdminUser = async () => {
        if (newAdminUser.name && newAdminUser.email && company?.id) {
            setError(null);
            setIsLoading(true);
            try {
                await companyUserCreate({
                    variables: {
                        input: {
                            companyId: company.id,
                            name: newAdminUser.name,
                            email: newAdminUser.email,
                            roles: [mapRoleToGraphQL(newAdminUser.role)]
                        }
                    }
                });
                setNewAdminUser({ name: '', email: '', role: 'ADMIN' as UserRole });
            } catch (error) {
                console.error('Error creating user:', error);
            }
        }
    };

    const removeAdminUser = async (user: CompanyUser) => {
        if (user?.id) {
            setError(null);
            setIsLoading(true);
            try {
                await companyUserDeactivate({
                    variables: {
                        companyUserId: user.id
                    }
                });
            } catch (error) {
            }
        }

    };

    const inviteUser = async (user: CompanyUser) => {
        if (user?.id) {
            setIsLoading(true);
            setError(null);
            try {
                await companyUserInvite({
                    variables: {
                        companyUserId: user.id
                    }
                });
            } catch (error) {
                console.error('Error inviting user:', error);
            }
        }
    };


    const getRoleIcon = (role: UserRole) => {
        switch (role) {
            case 'ADMIN':
                return <Shield className="h-4 w-4 text-red-500" />;
            case 'MANAGER':
                return <Shield className="h-4 w-4 text-yellow-500" />;
            default:
                return <User className="h-4 w-4 text-blue-500" />;
        }
    };

    const getRoleBadgeColor = (role: UserRole) => {
        switch (role) {
            case 'ADMIN':
                return 'bg-red-100 text-red-800';
            case 'MANAGER':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-blue-100 text-blue-800';
        }
    };

    const getStatusBadgeColor = (status: CompanyUserStatus) => {
        switch (status) {
            case CompanyUserStatus.Active:
                return 'bg-green-100 text-green-800';
            case CompanyUserStatus.Invited:
                return 'bg-yellow-100 text-yellow-800';
            case CompanyUserStatus.Created:
                return 'bg-blue-100 text-blue-800';
            case CompanyUserStatus.Inactive:
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    // Helper functions for user management restrictions
    const getAdminUsersCount = () => {
        return companyUsers.filter(user =>
            user?.roles?.includes(CompanyUserRole.Admin)
        ).length;
    };

    const isCurrentUser = (userEmail: string) => {
        return currentUser?.emailAddresses?.[0]?.emailAddress === userEmail;
    };

    const canDeleteUser = (user: CompanyUser) => {
        const isAdmin = user?.roles?.includes(CompanyUserRole.Admin);
        const adminCount = getAdminUsersCount();
        const isLoggedInUser = isCurrentUser(user?.email || '');

        // Can't delete if it's the only admin or if it's the current logged-in user
        if (isAdmin && (adminCount <= 1 || isLoggedInUser)) {
            return false;
        }

        return true;
    };

    const canInviteUser = (user: CompanyUser) => {
        return user?.status !== CompanyUserStatus.Active;
    };

    if (isLoading) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <ComponentLoading
                    message="Loading company users..."
                    className="min-h-[200px]"
                />
            </div>
        );
    }



    return (
        <div className="space-y-6">
            <div className="justify-start mb-8">
                <div className="flex items-center mb-2">
                    {/* <div className='p-1.5 bg-primary rounded-full mr-2'>
                        <Users className="h-4.5 w-4.5 text-white" />
                    </div> */}
                    <h2 className="text-2xl font-bold text-gray-900">Team Members</h2>
                </div>
                <p className="text-gray-600">
                    Add team members and configure their roles and permissions
                </p>
            </div>

            {(
                error && (
                    <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                        <p className="text-sm text-red-800">{error}</p>
                    </div>
                )
            )}

            {/* Admin Users Section */}
            <div className='bg-white rounded-lg p-6 border border-gray-200'>
                {/* Add New Admin User */}
                <div className="bg-gray-50 border border-gray-100 rounded-lg p-4 mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Add Team Member</h4>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                        <input
                            type="text"
                            placeholder="Full Name"
                            value={newAdminUser.name}
                            onChange={(e) => setNewAdminUser({ ...newAdminUser, name: e.target.value })}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                        <input
                            type="email"
                            placeholder="Email Address"
                            value={newAdminUser.email}
                            onChange={(e) => setNewAdminUser({ ...newAdminUser, email: e.target.value })}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                        <select
                            value={newAdminUser.role}
                            onChange={(e) => setNewAdminUser({ ...newAdminUser, role: e.target.value as UserRole })}
                            className="px-3 py-2 border border-gray-300  rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        >
                            {roles.map((role) => (
                                <option key={role.value} value={role.value}>
                                    {role.label}
                                </option>
                            ))}
                        </select>
                        <button
                            onClick={addAdminUser}
                            disabled={!newAdminUser.name || !newAdminUser.email || isLoading}
                            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                        >
                            {isLoading ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                            ) : (
                                <Plus className="h-4 w-4 mr-1" />
                            )}
                            {isLoading ? 'Creating...' : 'Add'}
                        </button>
                    </div>
                </div>

                {/* Admin Users List */}
                <div className="space-y-3">
                    {isLoading ? (
                        <div className="text-center py-8 text-gray-500">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500 mx-auto mb-4"></div>
                            <p>Loading team members...</p>
                        </div>
                    ) : (
                        <>
                            {companyUsers.map((user, index) => {
                                const canDelete = canDeleteUser(user);
                                const canInvite = canInviteUser(user);
                                return (
                                    <div key={index} className="flex items-center justify-between border border-gray-100 rounded-lg p-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3 min-w-0">
                                                <div className="w-10 h-10 bg-gray-200 rounded-full flex-shrink-0 flex items-center justify-center">
                                                    <User className="h-5 w-5 text-gray-600" />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <div className="font-medium text-gray-900 truncate">{user?.name}</div>
                                                    <div className="text-sm text-gray-600 truncate">{user?.email}</div>
                                                </div>
                                                <div className="flex items-center space-x-2 flex-shrink-0">
                                                    <div className="flex items-center space-x-2">
                                                        {getRoleIcon(user?.roles[0] as UserRole)}
                                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeColor(user?.roles[0] as UserRole)}`}>
                                                            {user?.roles[0]}
                                                        </span>
                                                    </div>
                                                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeColor(user?.status)}`}>
                                                        {user?.status}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {/* Invite Button */}
                                            <button
                                                onClick={() => inviteUser(user)}
                                                className={`p-2 rounded-lg transition-colors ${canInvite
                                                    ? 'text-blue-600 hover:bg-blue-50'
                                                    : 'text-gray-400 cursor-not-allowed opacity-50'
                                                    }`}
                                                title={
                                                    canInvite
                                                        ? (user?.status === CompanyUserStatus.Invited ? "Re-invite user" : "Invite user")
                                                        : "User is already active"
                                                }
                                                disabled={!canInvite}
                                            >
                                                <Mail className="h-4 w-4" />
                                            </button>
                                            {/* Delete Button */}
                                            <button
                                                onClick={() => removeAdminUser(user)}
                                                disabled={!canDelete}
                                                className={`p-2 rounded-lg transition-colors ${canDelete
                                                    ? 'text-red-600 hover:bg-red-50'
                                                    : 'text-gray-400 cursor-not-allowed opacity-50'
                                                    }`}
                                                title={
                                                    !canDelete
                                                        ? (isCurrentUser(user?.email || '')
                                                            ? "Cannot delete your own account"
                                                            : "Cannot delete the only admin user")
                                                        : "Delete user"
                                                }
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                );
                            })}
                        </>
                    )}
                    {companyUsers.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>No team members added yet</p>
                            <p className="text-sm">Add at least one team member to continue</p>
                        </div>
                    )}
                </div>
            </div>



            {/* Role Descriptions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-3">Role Permissions</h4>
                <div className="space-y-2">
                    {roles.map((role) => (
                        <div key={role.value} className="flex items-start space-x-2">
                            {getRoleIcon(role.value as UserRole)}
                            <div>
                                <span className="text-sm font-medium text-blue-900">{role.label}:</span>
                                <span className="text-sm text-blue-800 ml-1">{role.description}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default ComponentCompanyUserSetting;
