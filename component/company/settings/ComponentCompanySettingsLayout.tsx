'use client';

import React from 'react';
import SecondaryNavbar, { SecondaryNavbarLayout } from "@/component/common/ComponentSecondaryNavbar";
import { SecondaryNavItem } from "@/types/component/common/TypeSecondaryNavbar";
import { ArrowLeft, Building2, User } from "lucide-react";
import { useRouter } from "next/navigation";

export interface CompanySettingsLayoutProps {
    children: React.ReactNode;
}

const navItems: SecondaryNavItem[] = [
    {
        id: 'teammembers',
        label: 'Team Members',
        icon: <User className="h-5 w-5 " />,
        href: `/company/settings/team`,
    },
    {
        id: 'basicdetails',
        label: 'Basic Details',
        icon: <Building2 className="h-5 w-5" />,
        href: `/company/settings`,
    },
];

const ComponentCompanySettingsLayout: React.FC<CompanySettingsLayoutProps> = ({ children }) => {
    const router = useRouter();

    return (
        <div className="max-w-6xl mx-auto mb-10 mt-10">
            <div className='mb-10'>
                <SecondaryNavbarLayout
                    navbar={
                        <SecondaryNavbar
                            items={navItems}
                        />
                    }
                    content={children}
                />
            </div>
        </div>

    );
}

export default ComponentCompanySettingsLayout;