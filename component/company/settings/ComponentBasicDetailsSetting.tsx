'use client';

import React, { useEffect, useState } from 'react';
import { Building2, Mail, Phone, Globe, MapPin, AlertCircle, ArrowRight, Users, Pencil, Eye, Save } from 'lucide-react';
import { EntitySize, CompanyCreateBasicDetailsInput, CompanyUpdateBasicDetailsInput } from '@/lib/graphql/types/generated/graphql';
import { useGetCompanyBasicDetailsQuery, useCreateCompanyBasicDetailsMutation, useUpdateCompanyBasicDetailsMutation } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentTooltip from '@/component/common/ComponentTooltip';

interface ComponentCompanyBasicDetailsSettingProps {
    editMode: boolean;
    onSubmitSuccess?: () => void;
    initialCreate?: boolean;
}

const ModeToggle: React.FC<{
    isEditMode: boolean;
    onToggle: () => void;
    hasUnsavedChanges: boolean;
}> = ({ isEditMode, onToggle, hasUnsavedChanges }) => {
    return (
        <div className="flex items-center gap-3">
            {/* Mode indicator pill */}
            <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full border transition-colors duration-200 ${isEditMode
                ? 'bg-blue-50 border-blue-200 text-primary'
                : 'bg-gray-50 border-gray-200 text-secondary'
                }`}>
                {isEditMode ? (
                    <>
                        <Pencil className="w-3.5 h-3.5" />
                        <span className="text-xs font-medium">Edit Mode</span>
                    </>
                ) : (
                    <>
                        <Eye className="w-3.5 h-3.5" />
                        <span className="text-xs font-medium">View Mode</span>
                    </>
                )}
            </div>

            {/* Toggle switch */}
            <button
                type="button"
                onClick={onToggle}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${isEditMode ? 'bg-primary' : 'bg-gray-200'
                    }`}
                role="switch"
                aria-checked={isEditMode}
            >
                <span className="sr-only">{isEditMode ? 'Disable edit mode' : 'Enable edit mode'}</span>
                <span
                    className={`inline-block h-6 w-6 transform rounded-full bg-white transition duration-200 ease-in-out ${isEditMode ? 'translate-x-5' : 'translate-x-0.5'
                        }`}
                />
            </button>

            {/* Unsaved changes indicator */}
            {hasUnsavedChanges && (
                <span className="flex items-center gap-1 text-xs font-medium text-yellow-800 bg-yellow-50 px-2 py-1 rounded-full border border-yellow-200">
                    <span className="relative flex h-2 w-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-yellow-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-yellow-500"></span>
                    </span>
                    Unsaved
                </span>
            )}
        </div>
    );
};

const ComponentCompanyBasicDetailsSetting: React.FC<ComponentCompanyBasicDetailsSettingProps> = ({ editMode: initialEditMode = false, onSubmitSuccess, initialCreate = false }) => {
    // Local edit mode state
    const [isEditMode, setIsEditMode] = useState(initialEditMode);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    // Fetchers
    const { data, loading: fetcherLoading, error: queryError, refetch } = useGetCompanyBasicDetailsQuery();

    // State
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (fetcherLoading) {
            setIsLoading(true);
        } else {
            setIsLoading(false);
        }

        if (queryError) {
            setError(getUserFriendlyErrorMessage(queryError));
        }

        if (data?.getCompany?.basicDetails) {
            setFormData({
                name: data.getCompany.basicDetails.name,
                email: data.getCompany.basicDetails.email,
                website: data.getCompany.basicDetails.website,
                phoneNumber: data.getCompany.basicDetails.phoneNumber,
                industry: data.getCompany.basicDetails.industry,
                size: data.getCompany.basicDetails.size,
                address: data.getCompany.basicDetails.address
            });
            setError(null);
        }
    }, [fetcherLoading, queryError, data]);

    const [formData, setFormData] = useState<CompanyCreateBasicDetailsInput>({
        name: '',
        address: '',
        phoneNumber: '',
        email: '',
        website: '',
        industry: '',
        size: undefined
    });

    const [updateCompanyBasicDetails, { loading: updateMutationLoading }] = useUpdateCompanyBasicDetailsMutation({
        onCompleted: (data) => {
            setIsLoading(false);
            if (data?.companyUpdateBasicDetails?.basicDetails) {
                setFormData({
                    name: data.companyUpdateBasicDetails.basicDetails.name,
                    address: data.companyUpdateBasicDetails.basicDetails.address,
                    phoneNumber: data.companyUpdateBasicDetails.basicDetails.phoneNumber,
                    email: data.companyUpdateBasicDetails.basicDetails.email,
                    website: data.companyUpdateBasicDetails.basicDetails.website,
                    industry: data.companyUpdateBasicDetails.basicDetails.industry,
                    size: data.companyUpdateBasicDetails.basicDetails.size,
                });
                setError(null);
            }
        },
        onError: (apolloError) => {
            const errorMessage = getUserFriendlyErrorMessage(apolloError);
            setError(errorMessage);
        }
    });

    const [createCompanyBasicDetails, { loading: mutationLoading }] = useCreateCompanyBasicDetailsMutation({
        onCompleted: (data) => {
            if (mutationLoading) {
                setIsLoading(true);
            } else {
                setIsLoading(false);
            }

            if (data?.companyCreateBasicDetails?.basicDetails) {
                setFormData({
                    name: data.companyCreateBasicDetails.basicDetails.name,
                    address: data.companyCreateBasicDetails.basicDetails.address,
                    phoneNumber: data.companyCreateBasicDetails.basicDetails.phoneNumber,
                    email: data.companyCreateBasicDetails.basicDetails.email,
                    website: data.companyCreateBasicDetails.basicDetails.website,
                    industry: data.companyCreateBasicDetails.basicDetails.industry,
                    size: data.companyCreateBasicDetails.basicDetails.size,
                });
                setError(null);
            }
        },
        onError: (apolloError) => {
            const errorMessage = getUserFriendlyErrorMessage(apolloError);
            setError(errorMessage);
        }
    });

    const handleChange = (field: keyof CompanyCreateBasicDetailsInput, value: string | EntitySize) => {
        const newData = { ...formData, [field]: value };
        setFormData(newData);
        setHasUnsavedChanges(true);
    };

    const toggleEditMode = () => {
        if (isEditMode && hasUnsavedChanges) {
            // Show confirmation dialog or handle unsaved changes
            if (window.confirm('You have unsaved changes. Are you sure you want to exit edit mode?')) {
                setIsEditMode(false);
                setHasUnsavedChanges(false);
                // Reset form data to original data
                if (data?.getCompany?.basicDetails) {
                    setFormData({
                        name: data.getCompany.basicDetails.name,
                        email: data.getCompany.basicDetails.email,
                        website: data.getCompany.basicDetails.website,
                        phoneNumber: data.getCompany.basicDetails.phoneNumber,
                        industry: data.getCompany.basicDetails.industry,
                        size: data.getCompany.basicDetails.size,
                        address: data.getCompany.basicDetails.address
                    });
                }
            }
        } else {
            setIsEditMode(!isEditMode);
        }
    };

    const isFormValid = () => {
        return formData.name?.trim() !== '' &&
            formData.email?.trim() !== '' &&
            formData.website?.trim() !== '' &&
            formData.industry?.trim() !== '' &&
            (formData.size !== undefined && formData.size !== null);
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (isFormValid()) {
            setError(null);
            if (isEditMode && data?.getCompany?.id) {
                const graphqlInput: CompanyUpdateBasicDetailsInput = {
                    companyId: data?.getCompany?.id,
                    address: formData.address,
                    phoneNumber: formData.phoneNumber,
                    website: formData.website,
                    industry: formData.industry,
                    size: formData.size,
                };
                try {
                    setIsLoading(true)
                    await updateCompanyBasicDetails({
                        variables: {
                            input: graphqlInput
                        }
                    });
                    setIsEditMode(false);
                    setHasUnsavedChanges(false);
                    onSubmitSuccess?.();
                } catch (error) {
                    // Error handling is done in onError callback
                }
            } else {
                const graphqlInput: CompanyCreateBasicDetailsInput = {
                    name: formData.name,
                    email: formData.email,
                    website: formData.website,
                    address: formData.address || undefined,
                    phoneNumber: formData.phoneNumber || undefined,
                    industry: formData.industry || undefined,
                    size: formData.size || undefined,
                };

                try {
                    await createCompanyBasicDetails({
                        variables: {
                            input: graphqlInput
                        }
                    });
                    onSubmitSuccess?.();
                } catch (error) {
                    // Error handling is done in onError callback
                }
            }
        }
    };

    const industries = [
        'Technology',
        'Healthcare',
        'Finance',
        'Education',
        'Retail',
        'Manufacturing',
        'Consulting',
        'Real Estate',
        'Food & Beverage',
        'Other'
    ];

    const entitySizes = [
        { value: EntitySize.LessThanTen, label: '1-10 employees' },
        { value: EntitySize.TenToHundred, label: '11-100 employees' },
        { value: EntitySize.HundredToThousand, label: '101-1000 employees' },
        { value: EntitySize.GreaterThanThousand, label: '1000+ employees' }
    ];

    if (isLoading) {
        return (
            <div className={`bg-white rounded-lg border border-gray-200 p-6 space-y-6`}>
                <ComponentLoading
                    message="Loading company details..."
                    className="min-h-[200px]"
                />
            </div>
        )
    }

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
                <div className="mb-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
                        <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                        <div>
                            <h3 className="text-sm font-medium text-red-800">Form Submission Failed</h3>
                            <p className="text-sm text-red-700 mt-1">{error}</p>
                        </div>
                    </div>
                </div>
            )}
            <div className={`justify-start mb-8`}>
                <div className="flex items-center justify-between mb-4">
                    <div className="flex flex-col gap-1">
                        <h2 className="text-2xl font-bold text-gray-900">Basic Details</h2>
                        <p className="text-sm text-gray-600">
                            Add basic details about your company
                        </p>
                    </div>
                    <div className='mr-2'>
                        <ModeToggle
                            isEditMode={isEditMode}
                            onToggle={toggleEditMode}
                            hasUnsavedChanges={hasUnsavedChanges}
                        />
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-white border border-gray-200 rounded-lg p-6">
                {/* Company Name */}
                <div className="md:col-span-2">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Company Name *
                    </label>
                    <div className="relative">
                        <ComponentTooltip
                            content={(!initialCreate || !isEditMode) ? "Please reach out to us to update this <NAME_EMAIL>" : "Add your company name. This will be your official company name."}
                            position="top"
                            className='w-full'
                        >
                            <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                            <input
                                type="text"
                                id="name"
                                disabled={!initialCreate || !isEditMode}
                                value={formData.name}
                                onChange={(e) => handleChange('name', e.target.value)}
                                placeholder="Enter your company name"
                                className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed`}
                                required
                            />
                        </ComponentTooltip>
                    </div>
                </div>

                {/* Email */}
                <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Business Email *
                    </label>
                    <div className="relative">
                        <ComponentTooltip
                            content={(!initialCreate || !isEditMode) ? "Please reach out to us to update this <NAME_EMAIL>" : "Add your business email. This will be your official contact details."}
                            position="top"
                            className='w-full'
                        >
                            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                            <input
                                type="email"
                                id="email"
                                disabled={!initialCreate || !isEditMode}
                                value={formData.email}
                                onChange={(e) => handleChange('email', e.target.value)}
                                placeholder="<EMAIL>"
                                className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed`}
                                required
                            />
                        </ComponentTooltip>
                    </div>
                </div>

                {/* Website */}
                <div>
                    <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                        Website *
                    </label>
                    <div className="relative">
                        <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        <input
                            type="url"
                            id="website"
                            disabled={!isEditMode}
                            value={formData.website}
                            onChange={(e) => handleChange('website', e.target.value)}
                            placeholder="https://www.company.com"
                            className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${!isEditMode ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-white hover:bg-gray-50'}`}
                            required
                        />
                    </div>
                </div>

                {/* Phone Number */}
                <div>
                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                    </label>
                    <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        <input
                            type="tel"
                            id="phoneNumber"
                            disabled={!isEditMode}
                            value={formData.phoneNumber || ''}
                            onChange={(e) => handleChange('phoneNumber', e.target.value)}
                            placeholder="+****************"
                            className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${!isEditMode ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-white hover:bg-gray-50'}`}
                        />
                    </div>
                </div>

                {/* Industry */}
                <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
                        Industry *
                    </label>
                    <select
                        id="industry"
                        disabled={!isEditMode}
                        value={formData.industry || ''}
                        onChange={(e) => handleChange('industry', e.target.value)}
                        className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${!isEditMode ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-white hover:bg-gray-50'}`}
                    >
                        <option value="">Select an industry</option>
                        {industries.map((industry) => (
                            <option key={industry} value={industry}>
                                {industry}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Company Size */}
                <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company Size *
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {entitySizes.map((size) => (
                            <button
                                key={size.value}
                                type="button"
                                disabled={!isEditMode}
                                onClick={() => handleChange('size', size.value)}
                                className={` p-3 border rounded-lg text-sm font-medium transition-colors ${formData.size === size.value
                                    ? 'border-primary bg-blue-50 text-primary'
                                    : !isEditMode
                                        ? 'border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed'
                                        : 'border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50'
                                    }`}
                            >
                                {size.label}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Address */}
                <div className="md:col-span-2">
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                        Business Address
                    </label>
                    <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <textarea
                            id="address"
                            disabled={!isEditMode}
                            value={formData.address || ''}
                            onChange={(e) => handleChange('address', e.target.value)}
                            placeholder="Enter your business address"
                            rows={3}
                            className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none ${!isEditMode ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-white hover:bg-gray-50'}`}
                        />
                    </div>
                </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-800">
                    <strong>Note:</strong> Fields marked with * are required to proceed to the next step.
                    You can always update this information later in your company settings.
                </p>
            </div>

            {(
                <div className="flex justify-end mt-6">
                    <button
                        type="submit"
                        disabled={!isFormValid() || isLoading || !isEditMode || !hasUnsavedChanges}
                        className={`px-8 py-3 rounded-lg transition-all duration-200 flex items-center space-x-2
                            ${(!isFormValid() || isLoading || !isEditMode || !hasUnsavedChanges)
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : 'bg-primary text-white hover:bg-primary/90'
                            }`}
                    >
                        {isLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                <span>Saving...</span>
                            </>
                        ) : (
                            <>
                                <Save className="h-5 w-5" />
                                <span>Save Changes</span>
                            </>
                        )}
                    </button>
                </div>
            )}
        </form>
    );
};

export default ComponentCompanyBasicDetailsSetting;
