import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Users, FileText, DollarSign, TrendingUp, Calculator, Receipt } from 'lucide-react';

interface InsightCard {
    id: string;
    title: string;
    value: string | number;
    subtitle: string;
    trend?: {
        direction: 'up' | 'down' | 'neutral';
        value: string;
    };
    icon: React.ReactNode;
    color: string;
}

interface BusinessSection {
    id: string;
    name: string;
    icon: React.ReactNode;
    cards: InsightCard[];
}

const businessSections: BusinessSection[] = [
    {
        id: 'customers',
        name: 'Customers',
        icon: <Users className="w-5 h-5" />,
        cards: [
            {
                id: 'total-customers',
                title: 'Total Customers',
                value: 24,
                subtitle: 'Active customers',
                trend: { direction: 'up', value: '+3 this month' },
                icon: <Users className="w-4 h-4" />,
                color: 'bg-blue-500'
            },
            {
                id: 'new-customers',
                title: 'New This Month',
                value: 3,
                subtitle: 'New customers',
                trend: { direction: 'up', value: '+50% vs last month' },
                icon: <Users className="w-4 h-4" />,
                color: 'bg-green-500'
            },
            {
                id: 'customer-retention',
                title: 'Retention Rate',
                value: '85%',
                subtitle: 'Customer retention',
                trend: { direction: 'up', value: '+2% this quarter' },
                icon: <TrendingUp className="w-4 h-4" />,
                color: 'bg-emerald-500'
            }
        ]
    },
    {
        id: 'quotes',
        name: 'Quotes',
        icon: <FileText className="w-5 h-5" />,
        cards: [
            {
                id: 'pending-quotes',
                title: 'Pending Quotes',
                value: 8,
                subtitle: 'Awaiting approval',
                trend: { direction: 'neutral', value: '2 expiring soon' },
                icon: <FileText className="w-4 h-4" />,
                color: 'bg-orange-500'
            },
            {
                id: 'quote-value',
                title: 'Quote Value',
                value: '$89,850',
                subtitle: 'Total pending value',
                trend: { direction: 'up', value: '+15% this week' },
                icon: <Calculator className="w-4 h-4" />,
                color: 'bg-purple-500'
            },
            {
                id: 'conversion-rate',
                title: 'Conversion Rate',
                value: '68%',
                subtitle: 'Quote to sale',
                trend: { direction: 'up', value: '+5% this month' },
                icon: <TrendingUp className="w-4 h-4" />,
                color: 'bg-indigo-500'
            }
        ]
    },
    {
        id: 'accounting',
        name: 'Accounting',
        icon: <DollarSign className="w-5 h-5" />,
        cards: [
            {
                id: 'monthly-revenue',
                title: 'Monthly Revenue',
                value: '$12,500',
                subtitle: 'Current month',
                trend: { direction: 'up', value: '+8% vs last month' },
                icon: <DollarSign className="w-4 h-4" />,
                color: 'bg-primary'
            },
            {
                id: 'outstanding-invoices',
                title: 'Outstanding',
                value: '$4,200',
                subtitle: 'Pending payments',
                trend: { direction: 'down', value: '-12% this week' },
                icon: <Receipt className="w-4 h-4" />,
                color: 'bg-red-500'
            },
            {
                id: 'profit-margin',
                title: 'Profit Margin',
                value: '32%',
                subtitle: 'This month',
                trend: { direction: 'up', value: '+3% vs last month' },
                icon: <TrendingUp className="w-4 h-4" />,
                color: 'bg-cyan-500'
            }
        ]
    }
];

const ComponentInsightsCarousel: React.FC = () => {
    const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
    const [currentCardIndex, setCurrentCardIndex] = useState(0);

    const currentSection = businessSections[currentSectionIndex];
    const cardsPerView = 2; // Show 2 cards at a time
    const maxCardIndex = Math.max(0, currentSection.cards.length - cardsPerView);

    const nextSection = () => {
        setCurrentSectionIndex(prev => (prev + 1) % businessSections.length);
        setCurrentCardIndex(0); // Reset card index when changing sections
    };

    const prevSection = () => {
        setCurrentSectionIndex(prev => (prev - 1 + businessSections.length) % businessSections.length);
        setCurrentCardIndex(0); // Reset card index when changing sections
    };

    const nextCard = () => {
        setCurrentCardIndex(prev => Math.min(prev + 1, maxCardIndex));
    };

    const prevCard = () => {
        setCurrentCardIndex(prev => Math.max(prev - 1, 0));
    };

    const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
        switch (direction) {
            case 'up':
                return <TrendingUp className="w-3 h-3 text-green-600" />;
            case 'down':
                return <TrendingUp className="w-3 h-3 text-red-600 rotate-180" />;
            default:
                return null;
        }
    };

    const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
        switch (direction) {
            case 'up':
                return 'text-green-600';
            case 'down':
                return 'text-red-600';
            default:
                return 'text-gray-500';
        }
    };

    return (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 h-full flex flex-col">
            {/* Section Navigation */}
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                    <div className="text-primary">
                        {currentSection.icon}
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">{currentSection.name}</h3>
                        <p className="text-sm text-gray-500">Business insights</p>
                    </div>
                </div>
                <div className="flex space-x-1">
                    <button
                        onClick={prevSection}
                        className="p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 hover:shadow-sm"
                        title="Previous section"
                    >
                        <ChevronLeft className="w-4 h-4 text-gray-600" />
                    </button>
                    <button
                        onClick={nextSection}
                        className="p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 hover:shadow-sm"
                        title="Next section"
                    >
                        <ChevronRight className="w-4 h-4 text-gray-600" />
                    </button>
                </div>
            </div>

            {/* Section Indicators */}
            <div className="flex justify-center space-x-2 mb-6">
                {businessSections.map((section, index) => (
                    <button
                        key={section.id}
                        onClick={() => {
                            setCurrentSectionIndex(index);
                            setCurrentCardIndex(0);
                        }}
                        className={`flex items-center space-x-1 px-2 py-1 rounded-full transition-all duration-200 text-xs font-medium ${
                            index === currentSectionIndex
                                ? 'bg-primary text-white'
                                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        }`}
                        title={section.name}
                    >
                        <span className="w-3 h-3">{section.icon}</span>
                        <span className="hidden sm:inline">{section.name}</span>
                    </button>
                ))}
            </div>

            {/* Cards Carousel */}
            <div className="flex-1 flex flex-col">
                <div className="flex items-center justify-between mb-4">
                    <h4 className="text-sm font-medium text-gray-700">Metrics</h4>
                    {currentSection.cards.length > cardsPerView && (
                        <div className="flex space-x-1">
                            <button
                                onClick={prevCard}
                                disabled={currentCardIndex === 0}
                                className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            >
                                <ChevronLeft className="w-3 h-3 text-gray-500" />
                            </button>
                            <button
                                onClick={nextCard}
                                disabled={currentCardIndex >= maxCardIndex}
                                className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            >
                                <ChevronRight className="w-3 h-3 text-gray-500" />
                            </button>
                        </div>
                    )}
                </div>

                <div className="overflow-hidden flex-1">
                    <div
                        className="flex transition-transform duration-300 ease-in-out h-full"
                        style={{ transform: `translateX(-${currentCardIndex * (100 / cardsPerView)}%)` }}
                    >
                        {currentSection.cards.map((card) => (
                            <div key={card.id} className="w-1/2 flex-shrink-0 px-1">
                                <div className="bg-gray-50 rounded-lg p-4 h-full border border-gray-100 hover:shadow-md hover:bg-white transition-all duration-300 cursor-pointer group">
                                    <div className="flex items-start justify-between mb-3">
                                        <div className={`p-2 rounded-lg ${card.color} text-white group-hover:scale-105 transition-transform duration-300 shadow-sm`}>
                                            {card.icon}
                                        </div>
                                        {card.trend && (
                                            <div className="flex items-center space-x-1">
                                                {getTrendIcon(card.trend.direction)}
                                            </div>
                                        )}
                                    </div>

                                    <div className="space-y-1">
                                        <h4 className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                                            {card.title}
                                        </h4>
                                        <p className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                                            {card.value}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                            {card.subtitle}
                                        </p>
                                        {card.trend && (
                                            <p className={`text-xs font-medium ${getTrendColor(card.trend.direction)}`}>
                                                {card.trend.value}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Card Navigation Dots */}
                {currentSection.cards.length > cardsPerView && (
                    <div className="flex justify-center space-x-1 mt-4">
                        {Array.from({ length: maxCardIndex + 1 }).map((_, index) => (
                            <button
                                key={index}
                                onClick={() => setCurrentCardIndex(index)}
                                className={`w-1.5 h-1.5 rounded-full transition-all duration-200 ${
                                    index === currentCardIndex ? 'bg-gray-600' : 'bg-gray-300'
                                }`}
                            />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default ComponentInsightsCarousel;
