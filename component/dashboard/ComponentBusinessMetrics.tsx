'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Minus, Users, ShoppingCart, DollarSign } from 'lucide-react';
import { BusinessMetric } from '@/types/component/dashboard/TypeBusinessMetrics';

const MetricCard: React.FC<{ metric: BusinessMetric }> = ({ metric }) => {
    const formatValue = (value: number | string, format?: string) => {
        if (typeof value === 'string') return value;

        switch (format) {
            case 'currency':
                return `$${value.toLocaleString()}`;
            case 'percentage':
                return `${value}%`;
            default:
                return value.toLocaleString();
        }
    };

    const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
        switch (direction) {
            case 'up':
                return <TrendingUp className="h-4 w-4 text-green-600" />;
            case 'down':
                return <TrendingDown className="h-4 w-4 text-red-600" />;
            default:
                return <Minus className="h-4 w-4 text-gray-400" />;
        }
    };

    const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
        switch (direction) {
            case 'up':
                return 'text-green-600';
            case 'down':
                return 'text-red-600';
            default:
                return 'text-gray-500';
        }
    };





    return (
        <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-full ${metric.color || 'bg-primary'}`}>
                        <div className="text-white">
                            {metric.icon}
                        </div>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-600">{metric.label}</p>
                        <p className="text-2xl font-bold text-gray-900">
                            {formatValue(metric.value, metric.format)}
                        </p>
                    </div>
                </div>
            </div>

            {metric.trend && (
                <div className="mt-4 flex items-center space-x-2">
                    {getTrendIcon(metric.trend.direction)}
                    <span className={`text-sm font-medium ${getTrendColor(metric.trend.direction)}`}>
                        {metric.trend.percentage}%
                    </span>
                    <span className="text-sm text-gray-500">
                        vs {metric.trend.period}
                    </span>
                </div>
            )}
        </div>
    );
};

const ComponentBusinessMetrics: React.FC = () => {

    const businessMetrics: BusinessMetric[] = [
        {
            id: 'active-customers',
            label: 'Active Customers',
            value: 24,
            icon: <Users className="h-6 w-6" />,
            trend: {
                direction: 'up',
                percentage: 12,
                period: 'last month'
            },
            color: 'bg-primary'
        },
        {
            id: 'active-orders',
            label: 'Active Orders',
            value: 8,
            icon: <ShoppingCart className="h-6 w-6" />,
            trend: {
                direction: 'up',
                percentage: 25,
                period: 'last week'
            },
            color: 'bg-green-500'
        },
        {
            id: 'monthly-revenue',
            label: 'Monthly Revenue',
            value: 12500,
            icon: <DollarSign className="h-6 w-6" />,
            format: 'currency',
            trend: {
                direction: 'up',
                percentage: 8,
                period: 'last month'
            },
            color: 'bg-blue-500'
        },
        {
            id: 'growth-rate',
            label: 'Growth Rate',
            value: 15.5,
            icon: <TrendingUp className="h-6 w-6" />,
            format: 'percentage',
            trend: {
                direction: 'up',
                percentage: 3,
                period: 'last quarter'
            },
            color: 'bg-purple-500'
        }
    ];

    if (!businessMetrics || businessMetrics.length === 0) {
        return (
            <div className={`bg-white rounded-lg border border-gray-200 p-8 text-center`}>
                <div className="text-gray-500">
                    <div className="text-lg font-medium mb-2">No metrics available</div>
                    <p className="text-sm">Start adding customers and services to see your business insights.</p>
                </div>
            </div>
        );
    }

    return (
        <div>
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900">Business Overview</h2>
                <p className="text-gray-600 mt-1">Key metrics for your business performance</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {businessMetrics.map((metric) => (
                    <MetricCard key={metric.id} metric={metric} />
                ))}
            </div>
        </div>
    );
};

export default ComponentBusinessMetrics;
