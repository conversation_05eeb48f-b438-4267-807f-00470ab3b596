'use client';

import React, { useState } from 'react';
import {
    Check<PERSON>ircle,
    Circle,
    AlertCircle,
    Clock,
    ArrowRight,
    Calendar
} from 'lucide-react';
import { TaskItem } from '@/types/component/dashboard/TypeBusinessMetrics';

const TaskItemCard: React.FC<{
    task: TaskItem;
    onComplete: () => void;
    onClick: () => void;
}> = ({ task, onComplete, onClick }) => {
    const getPriorityColor = (priority: 'high' | 'medium' | 'low') => {
        switch (priority) {
            case 'high':
                return 'bg-red-100 text-red-800 border-red-200';
            case 'medium':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'low':
                return 'bg-green-100 text-green-800 border-green-200';
        }
    };

    const getPriorityIcon = (priority: 'high' | 'medium' | 'low') => {
        switch (priority) {
            case 'high':
                return <AlertCircle className="h-4 w-4" />;
            case 'medium':
                return <Clock className="h-4 w-4" />;
            case 'low':
                return <Circle className="h-4 w-4" />;
        }
    };

    const formatDueDate = (dueDate?: string) => {
        if (!dueDate) return null;
        const date = new Date(dueDate);
        const now = new Date();
        const diffTime = date.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return 'Overdue';
        if (diffDays === 0) return 'Due today';
        if (diffDays === 1) return 'Due tomorrow';
        return `Due in ${diffDays} days`;
    };

    return (
        <div className={`bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-all duration-200 ${task.completed ? 'opacity-75 bg-gray-50' : ''
            }`}>
            <div className="flex items-start space-x-3">
                <button
                    onClick={onComplete}
                    className={`mt-1 flex-shrink-0 ${task.completed
                        ? 'text-green-600'
                        : 'text-gray-400 hover:text-primary'
                        } transition-colors`}
                >
                    {task.completed ? (
                        <CheckCircle className="h-5 w-5" />
                    ) : (
                        <Circle className="h-5 w-5" />
                    )}
                </button>

                <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <h3 className={`text-sm font-medium ${task.completed ? 'line-through text-gray-500' : 'text-gray-900'
                                }`}>
                                {task.title}
                            </h3>
                            <p className={`text-sm mt-1 ${task.completed ? 'text-gray-400' : 'text-gray-600'
                                }`}>
                                {task.description}
                            </p>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)
                                }`}>
                                {getPriorityIcon(task.priority)}
                                <span className="ml-1 capitalize">{task.priority}</span>
                            </span>
                        </div>
                    </div>

                    <div className="flex items-center justify-between mt-3">
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span className="bg-gray-100 px-2 py-1 rounded-full">
                                {task.category}
                            </span>
                            {task.dueDate && (
                                <span className="flex items-center">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    {formatDueDate(task.dueDate)}
                                </span>
                            )}
                        </div>

                        {task.actionUrl && !task.completed && (
                            <button
                                onClick={onClick}
                                className="flex items-center text-xs text-primary hover:text-primary-hover transition-colors"
                            >
                                <span>Take action</span>
                                <ArrowRight className="h-3 w-3 ml-1" />
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

const ComponentTaskChecklist = () => {

    const [tasks, setTasks] = useState<TaskItem[]>([
        {
            id: '1',
            title: 'Add your first service',
            description: 'Create a service offering to start accepting orders from customers',
            priority: 'high',
            completed: false,
            category: 'Setup',
            actionUrl: '/services/new'
        },
        {
            id: '2',
            title: 'Import customer data',
            description: 'Upload your existing customer list or add customers manually',
            priority: 'medium',
            completed: false,
            category: 'Customers',
            actionUrl: '/customers/new'
        },
        {
            id: '3',
            title: 'Configure payment methods',
            description: 'Set up payment processing to accept payments from customers',
            priority: 'high',
            completed: false,
            category: 'Payments',
            dueDate: '2024-12-31'
        }
    ]);

    const pendingTasks = tasks.filter(task => !task.completed);
    const highPriorityTasks = pendingTasks.filter(task => task.priority === 'high');
    const completedTasks = tasks.filter(task => task.completed);

    if (tasks.length === 0) {
        return (
            <div className={`bg-white rounded-lg border border-gray-200 p-8 text-center`}>
                <div className="text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                    <div className="text-lg font-medium mb-2">All caught up!</div>
                    <p className="text-sm">No pending tasks at the moment.</p>
                </div>
            </div>
        );
    }

    return (
        <div>
            <div className="mb-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-xl font-bold text-gray-900">Tasks & Recommendations</h2>
                        <p className="text-gray-600 mt-1">
                            {pendingTasks.length} pending task{pendingTasks.length !== 1 ? 's' : ''}
                            {highPriorityTasks.length > 0 && (
                                <span className="text-red-600 font-medium">
                                    {' '}• {highPriorityTasks.length} high priority
                                </span>
                            )}
                        </p>
                    </div>
                    <div className="text-right">
                        <div className="text-sm text-gray-500">
                            {completedTasks.length}/{tasks.length} completed
                        </div>
                        <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                            <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${(completedTasks.length / tasks.length) * 100}%` }}
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div className="space-y-3">
                {/* High Priority Tasks First */}
                {highPriorityTasks.map((task) => (
                    <TaskItemCard
                        key={task.id}
                        task={task}
                        onComplete={() => { }}
                        onClick={() => { }}
                    />
                ))}

                {/* Other Pending Tasks */}
                {pendingTasks.filter(task => task.priority !== 'high').map((task) => (
                    <TaskItemCard
                        key={task.id}
                        task={task}
                        onComplete={() => { }}
                        onClick={() => { }}
                    />
                ))}

                {/* Completed Tasks (collapsed) */}
                {completedTasks.length > 0 && (
                    <details className="mt-6">
                        <summary className="cursor-pointer text-sm font-medium text-gray-600 hover:text-gray-800">
                            View {completedTasks.length} completed task{completedTasks.length !== 1 ? 's' : ''}
                        </summary>
                        <div className="mt-3 space-y-3">
                            {completedTasks.map((task) => (
                                <TaskItemCard
                                    key={task.id}
                                    task={task}
                                    onComplete={() => { }}
                                    onClick={() => { }}
                                />
                            ))}
                        </div>
                    </details>
                )}
            </div>
        </div>
    );
};

export default ComponentTaskChecklist;
