# Rule Page 1: Styling and Theming of the Application

This document outlines the styling and theming conventions for the OnePlatform frontend. Following these rules ensures a consistent, maintainable, and visually appealing user experience. This guide is intended for other LLMs to use when building new features.

## 1. Overall Philosophy

Our design philosophy is centered on creating a clean, modern, and intuitive interface. We prioritize clarity and ease of use. The styling is minimalist, with a clear visual hierarchy. We use a consistent color palette, typography, and spacing to create a cohesive experience across the application.

## 2. Colors

The application uses a specific color palette defined in `app/styles/_variables.css`.

-   **Primary Color**: `#3B5BA5` (a shade of blue). Used for primary actions, links, and highlights. The corresponding CSS class is `.bg-primary`.
-   **Secondary Color**: `#E87A5D` (a shade of orange). Used for secondary actions and accents. The corresponding CSS class is `.bg-secondary`.
-   **Background Color**: `#f8f8f8`. The default background color for most of the application. The main layout `<body>` tag has a subtle gradient: `bg-gradient-primary-to-secondary`.
-   **Text Color**: `#1A1A1A` (a dark gray). The default text color is `.text-default`.
-   **White**: `#e0e0e0`. Used for text on dark backgrounds.
-   **Opacity**: Use opacity variants for backgrounds and text where needed, e.g., `bg-default-gray-20-opacity` or `bg-white-40-opacity`.

**Example:**

```tsx
// Primary Button
<button className="bg-primary text-white ...">Click Me</button>

// Page Container
<div className="bg-white rounded-lg shadow-sm border border-gray-200">
    {/* Page content goes here */}
</div>
```

## 3. Typography

All typography is based on the **DM Sans** font, which is loaded in `app/layout.tsx`.

-   **Font Family**: `DM Sans` is used for both body text and headings (`--font-body` and `--font-heading`).
-   **Base Font Size**: `14px` with a `line-height` of `1.5`.
-   **Font Weight**:
    -   Body: `500`
    -   Headings (`h1`-`h6`): `600`

**Example:**

```tsx
<h1 className="text-3xl font-bold tracking-tight text-gray-900">Page Title</h1>
<p className="mt-1 text-md text-gray-600">This is a paragraph of body text.</p>
```

## 4. Layout and Spacing

-   **Max Width Container**: Page content is typically wrapped in a container with `max-w-7xl` (`72rem`), centered with `mx-auto`, and has horizontal padding (`px-4 sm:px-6 lg:px-8`).
-   **Component Containers**: Components that group content, like the `CustomersContainer`, use a common styling pattern: `bg-white rounded-lg shadow-sm border border-gray-200`. This creates a consistent "card" look.
-   **Margins and Padding**: Use Tailwind's spacing utilities (`m-`, `p-`, `gap-`) for consistent spacing. Default margins between sections are often `mb-8`.

## 5. Borders and Shadows

-   **Border Radius**: We use `rounded-md` (0.375rem), `rounded-lg` (0.5rem), and `rounded-xl` (0.75rem) for varying levels of rounded corners. `rounded-lg` is the most common.
-   **Drop Shadows**: Standard box shadows are defined in `app/styles/_shadows.css` and correspond to Tailwind's `shadow-md`, `shadow-lg`, and `shadow-xl`. `shadow-sm` is used on component containers.

## 6. Creating New Routes

To create a new route, add a new folder inside the `app` directory. The folder name will be the route's path. Inside that folder, create a `page.tsx` file which will be the main component for that route.

**Example: Creating a `/reports` route**

1.  Create a new directory: `app/reports/`
2.  Create the page file: `app/reports/page.tsx`
3.  Implement the page component in `app/reports/page.tsx`.

```tsx
// app/reports/page.tsx
const ReportsPage = () => {
  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <h1 className="text-3xl font-bold">Reports</h1>
      {/* ... rest of the page */}
    </main>
  );
};

export default ReportsPage;
```

## 7. Tabular Data: Use List View, Not `<table>`

When displaying tabular data, we avoid using the traditional `<table>` HTML element. Instead, we use a flexbox-based list view pattern. This provides more flexibility for responsive design.

The `ComponentAllCustomersTable.tsx` is the primary example of this.

**Key characteristics:**

-   A main container with `flex flex-col`.
-   A header row built with `div`s and flexbox.
-   Each data row is a `div` with `flex`.
-   Each "cell" is a `div` with `flex-1` to ensure columns align.
-   The list is scrollable on smaller screens using `overflow-x-auto`.

**Example Structure:**

```tsx
<div className="flex flex-col">
    {/* Header */}
    <div className="flex bg-gray-50 border-b">
        <div className="px-6 py-3 flex-1">Name</div>
        <div className="px-6 py-3 flex-1">Email</div>
    </div>
    {/* Rows */}
    <div>
        {items.map(item => (
            <div key={item.id} className="flex border-b hover:bg-gray-50">
                <div className="px-6 py-4 flex-1">{item.name}</div>
                <div className="px-6 py-4 flex-1">{item.email}</div>
            </div>
        ))}
    </div>
</div>
```

## 8. Common Styles and Tailwind CSS

-   **Mandatory Styling Patterns**: If a set of styles is used in multiple places, it should be extracted into a common class in the `app/styles/` directory.
-   **Creating New Style Files**: If the new style doesn't fit into an existing file in `app/styles/`, create a new one and import it into `app/globals.css`.
-   **Combining Properties in Tailwind**: Use Tailwind's `@apply` directive in CSS files or plugins in `tailwind.config.ts` to combine frequently used properties. For example, a common button style can be composed into a single class.

**Example: Creating a component in `tailwind.config.ts`**

If a primary button is always styled the same, you could add this to your `tailwind.config.ts`:

```ts
// tailwind.config.ts
// ...
plugins: [
    function({ addComponents }) {
      addComponents({
        '.btn-primary': {
          '@apply px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors': {},
        }
      })
    }
],
// ...
```

Then in your components, you can just use `<button className="btn-primary">`.

## 9. Icons

We use `lucide-react` for icons. They are easy to use and style. When to use icons:

-   In buttons to provide visual cues (`UserPlus` icon for "Add Customer").
-   For actions in lists/tables (`Trash`, `MoreHorizontal`).
-   In search bars and inputs.

Icons should generally be accompanied by text for accessibility, unless the context makes the icon's meaning obvious (like a trash icon for delete).

## 10. Responsive Design

Responsive design is a core principle. All new components and pages must be tested on various screen sizes.

-   **Mobile-First Approach**: Design for small screens first, then use Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`) to add styles for larger screens.
-   **Flexbox and Grid**: Use flexbox and CSS grid for layouts as they are inherently flexible.
-   **Hidden Elements**: Use `hidden` and responsive variants (e.g., `md:block`) to show or hide elements on different screen sizes.
-   **Scrolling**: For wide content like our list views, use `overflow-x-auto` on a container to allow horizontal scrolling on small devices without breaking the page layout.

An LLM creating a new component must ensure it is responsive and adapts gracefully to different viewports. 