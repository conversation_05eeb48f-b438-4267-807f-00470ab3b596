## Overall Instruction ## 
1. With the current structure, theres been a set of feedback that needs to be implemented. I will give all the main tasks and their sub tasks which elucidate how each main task needs to be executed
2. All the tasks are independent and mutually exclusive. If a task is dependent on any other task, I will explicitly specificy the dependency
3. You need to index the existing files and understand the context for each task before executing individual task
4. Till one task is not completed, do not move onto the other task, meaning sequential task thinking is required across all main tasks as well as in each individual main task
5. Indentation in `-` means hierarchial subtasks and (or) more information about the task
6. All `@fileName` represents `.tsx` files where the changes need to be made or to be used for context building
7. Collect all of these files first for each of the main task, build context and execute thereafter for efficient results

### Main Tasks ##
#### Task 1 ####
**Goal** 
Need to display dimensions in the @ComponentProduct page & make other cosmetic changes
*Subtasks*
1. Showing Dimensions 
- In @componentProduct we currently show all the master products
- For each of the master product we need to extract dimensions and show them as well
- We should align with the overall theme of the component design language before making changes
- The dimensions are a key value pair and need to be represented in a subtle way
  - Take reference of displaying dimensions in @ComponentAddProductForm where we generate dimensions as a subtle tag as part of the displayName variable
  - Implement similar design but a lot more subtle and less overbearing to go with the overall theme

2. Cosmentic Changes
- In the @ComponentQuotes we show all the quotes of the user
- We need to add a new column to show the `Customer Accepted Date`
- This field is currently unavailable on the graph, add a place holder for now
- In the same table, we show `actions`, remove it

#### Task 2 ####
**Goal**
UI changes for @ComponentCustomTagInput to support better UX
*Subtasks*
1. Change the UI UX of the @ComponentCustomTagInput's fields
- The label is the key and the value is the value and is represented in a card like fashion
- The existing data is also represented that way, and is not great UX
- Create two separate views (in the same component) for viewing and editing/inserting
  - Viewing should be a tag which shows the key:value similar to that of dimensions in @ComponentAddProductForm 
    - However, this view should be much more compact and should represent the notion of a tag
    - view should also give a `x` using lucide icons
    - this `x` will immediately delete the tag from the list
  - Editing should be in a card like fashion, where label and value can be edited
    - Label and view should have different UI representations (via color, labels, etc) for better readability and UX
    - The editing view should allow for deletion as well similar to `x` using lucide icon
    - The editing view should have a confirm CTA or when the user clicks outside the view, the changes should be saved
- However, the mutation call for saving all these tags only happens once the CTA to `save additional details` is clicked. No changes here!

#### Task 3 ####
**Goal** 
UI changes for @ComponentAllCustomersTable
*Subtasks*
1. Table level changes
- Show website as a column in the table. The graph type already has the data, so populate it
- Remove columns headers `type` which represent whether a customer is individual or business 
 - Instead, merge this `type` with `name` header value
 - Replace text representation of `type` with an `lucide-icon` that can better represent an invidividual vs business
- Change the copy of the subheading below the `Customers` text which is the core heading of the page to something sensible and meaningful
- Add a new column called `tags` that will be a comma separated color set of tags
 - Currently, no graphql type exists for this, so add a placeholder for now
 - Use colors sensibly and use colors that complement the primary and secondary colors from @_variables.css

 2. Customer Detailed View Changes
 - Customer Detailed view is represented in @componentCustomerDetailLayout.tsx and is represented as grid
 - Change the layout to be flex based layout where one component sits below the other
 - First Component is basic details about the customer
    - Components of the first card includes `name`, `email`, `phone`, `type`, `status`
    - Use horizontal space more than vertical space for this section
    - Status should be represented by color and is already that way. It needs to be tagged to a customer name
    - Name should be the most prevalent element of this section
    - Use `lucide-icons` for type representation and tag it to the name
    - Email, phone and tags should be represented together in a horizontal manner in a much subdued way
 - Second Component is customer insights
    - This component should be represented in a card like fashion with one card for one insight - `ARR`, `total products`, `number of quotes`, 
    - The card should have a header and a body
    - The header should have a title and a subheading
    - Create carousel with a set of elements as a common component and include carousel for this insights section
    - Horizontal space needs to be used more than vertical space
 - Third Component is similar to non-basic details components in @ComponentAddCustomerForm.tsx - notes, documents, custom tags, assignments
   - Give an edit that can open up edit section similar to that of @ComponentAddCustomerForm.tsx additional section 
   - Allow users to save this additional information from this view as well
   - Follow the same pattern of a `save additional information` button and call the same mutation to achieve this functionality

