const responses: { [key: string]: string[] } = {
    greeting: [
        "Hello! How can I assist you today?",
        "Hi there! What can I help you with?",
        "Welcome! I'm here to help you with your business. What's on your mind?",
    ],
    farewell: [
        "Goodbye! Have a great day!",
        "See you later! Feel free to come back if you have more questions.",
        "<PERSON><PERSON><PERSON>! Don't hesitate to reach out again.",
    ],
    help: [
        "I can help you with a variety of tasks, such as managing customers, tracking services, and providing insights into your business.",
        "You can ask me to create a new customer, find a service, or show you a summary of your monthly revenue.",
        "I'm here to streamline your workflow. Try asking something like 'Show me all active customers' or 'What are my top-selling services?'.",
    ],
    "new customer": [
        "I'd be happy to help you create a new customer! I can guide you through adding their basic information, contact details, and any initial notes. Would you like to start with an individual customer or a business customer?",
        "Great choice! Creating a new customer is easy. I'll need some basic information like their name, email, and contact details. Should we begin?",
    ],
    "new proposal": [
        "I can help you create a professional proposal! I'll guide you through selecting a customer, adding services, and customizing the proposal content. Which customer is this proposal for?",
        "Perfect! Let's create a compelling proposal. First, I'll need to know which customer this is for, then we can add the services and customize the details.",
    ],
    "generate quote": [
        "I'll help you generate a quote quickly! I can pull from your existing services and create a professional quote. Which customer needs this quote?",
        "Excellent! Creating a quote is straightforward. I'll help you select the customer, add services with pricing, and format everything professionally.",
    ],
    "customer insights": [
        "Here are some key customer insights from your data:\n\n📊 **Customer Overview:**\n• Total active customers: 24\n• New customers this month: 3 (+50% vs last month)\n• Average customer value: $520\n\n🎯 **Top Performing Segments:**\n• Business customers: 68% of revenue\n• Repeat customers: 85% retention rate\n\nWould you like me to dive deeper into any specific area?",
        "I can show you valuable customer insights! Your customer base is growing steadily with 24 active customers and strong retention rates. Business customers are your highest value segment. What specific customer metrics would you like to explore?",
    ],
    "accounting records": [
        "I can help you manage your accounting records efficiently! Here's what I can assist with:\n\n💰 **Current Status:**\n• Monthly revenue: $12,500\n• Outstanding invoices: $4,200\n• Recent payments: $8,300\n\n📋 **Available Actions:**\n• Review pending invoices\n• Generate financial reports\n• Track payment status\n\nWhat would you like to focus on?",
        "Let me help you with your accounting records! Your current monthly revenue is $12,500 with some outstanding invoices to follow up on. I can help you review payments, generate reports, or track invoice status. What's your priority?",
    ],
    default: [
        "I'm not sure I understand. Could you please rephrase that?",
        "That's an interesting question. Let me think about that.",
        "I'm still learning, but I'll do my best to help.",
        "Let's try something else. What would you like to do?",
    ],
};

const keywords: { [key: string]: string[] } = {
    greeting: ["hello", "hi", "hey"],
    farewell: ["bye", "goodbye", "see you"],
    help: ["help", "assist", "what can you do"],
    "new customer": ["create a new customer", "add customer", "new customer"],
    "new proposal": ["create a new proposal", "new proposal", "generate proposal"],
    "generate quote": ["generate a quote", "create quote", "new quote"],
    "customer insights": ["customer insights", "show me customer", "customer data", "customer analytics"],
    "accounting records": ["accounting records", "manage accounting", "financial records", "accounting help"],
};

const getResponseType = (message: string): string => {
    const lowerCaseMessage = message.toLowerCase();
    for (const type in keywords) {
        if (keywords[type].some(keyword => lowerCaseMessage.includes(keyword))) {
            return type;
        }
    }
    return "default";
};

export const getMockAIResponse = (message: string): Promise<string> => {
    return new Promise(resolve => {
        const responseType = getResponseType(message);
        const possibleResponses = responses[responseType];
        const response = possibleResponses[Math.floor(Math.random() * possibleResponses.length)];

        setTimeout(() => {
            resolve(response);
        }, Math.random() * 2000 + 1000); // Simulate 1-3 second delay
    });
}; 