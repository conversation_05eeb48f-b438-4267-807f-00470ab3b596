import { CustomField } from '@/types/common/TypeCustomField';
import React from 'react';

/**
 * Handles input changes for custom fields, extracting the value and passing it to the provided callback.
 * @param _e - The input change event
 * @param index - The index of the custom field
 * @param handleCustomFieldValueChange - Callback to update the custom field value
 */
export function handleCustomFieldInputChange(
    _e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
    index: number,
    handleCustomFieldValueChange: (value: any, index: number) => void
) {
    const target = _e.target as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    let newValue: any;
    if (target.type === 'checkbox') {
        newValue = (target as HTMLInputElement).checked;
    } else if (target.type === 'file') {
        // Skip file input for now, or handle as needed
        return;
    } else if ('multiple' in target && target.multiple) {
        newValue = Array.from((target as HTMLSelectElement).selectedOptions).map((o: any) => o.value);
    } else {
        newValue = target.value;
    }
    handleCustomFieldValueChange(newValue, index);
}

export function updateCustomFieldValue(
    customFields: CustomField[],
    value: any,
    index: number
): CustomField[] {
    return customFields.map((field, i) =>
        i === index ? { ...field, value, defaultValue: value } : field
    );
}

export function addCustomField(
    customFields: CustomField[],
    newField: CustomField
): CustomField[] {
    return [...customFields, { ...newField }];
}

export function deleteCustomField(
    customFields: CustomField[],
    index: number
): CustomField[] {
    return customFields.filter((_, i) => i !== index);
} 