import { LucideIcon } from 'lucide-react';

export interface SecondaryNavItem {
    id: string;
    label: string;
    icon: React.ReactElement<LucideIcon>;
    href: string;
}

export interface SecondaryNavbarProps {
    items: SecondaryNavItem[];
    className?: string;
}

export interface SecondaryNavbarLayoutProps {
    navbar: React.ReactNode;
    content: React.ReactNode;
    className?: string;
}

// Tooltip-related types
export type TooltipPosition = 'top' | 'bottom' | 'left' | 'right' | 'auto';

export interface TooltipProps {
    content: string;
    position?: TooltipPosition;
    delay?: number;
    children: React.ReactNode;
    className?: string;
    disabled?: boolean;
}
