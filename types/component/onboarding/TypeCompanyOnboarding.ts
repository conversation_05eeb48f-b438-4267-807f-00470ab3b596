import { CompanyOnboardingStep } from "@/lib/graphql/types/generated/graphql";


export interface OnboardingStepCardProps {
    step: CompanyOnboardingStep;
    onComplete: () => void;
    onSkip?: () => void;
    isActive?: boolean;
}


export interface CompanyAccountingDetailsInput {
    taxId?: string;
    currency: string;
    fiscalYearStart?: string;
    accountingMethod?: 'CASH' | 'ACCRUAL';
    defaultTaxRate?: number;
}

export interface CompanyUsersDetailsInput {
    adminUsers: {
        name: string;
        email: string;
        role: 'ADMIN' | 'MANAGER' | 'USER';
    }[];
    inviteUsers?: {
        email: string;
        role: 'ADMIN' | 'MANAGER' | 'USER';
    }[];
}

export interface CompanyCatalogDetailsInput {
    services: {
        name: string;
        description?: string;
        price?: number;
        category?: string;
    }[];
    products?: {
        name: string;
        description?: string;
        price?: number;
        sku?: string;
    }[];
}

export interface CompanyOnboardingFormData {
    accountingDetails: CompanyAccountingDetailsInput;
    usersDetails: CompanyUsersDetailsInput;
    catalogDetails: CompanyCatalogDetailsInput;
}

export interface CompanyOnboardingFormProps {
    onComplete: (data: CompanyOnboardingFormData) => void;
    onCancel: () => void;
}
