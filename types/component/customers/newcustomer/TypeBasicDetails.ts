import { CustomField } from '@/types/common/TypeCustomField';
import { ContactType, CustomerStage, CustomerType, EntitySize } from '@/lib/graphql/types/generated/graphql';

export interface IndividualCustomerForm {
    type: CustomerType.Individual;
    name: string;
    email: string;
    phone?: string;
    referralSource?: string;
    stage: CustomerStage;
    customFields: CustomField[];
}

export interface BusinessCustomerForm {
    type: CustomerType.Business;
    stage: CustomerStage;
    companyLegalName: string;
    companySize: EntitySize;
    industry: string;
    companyWebsite: string;
    contactName: string;
    contactTitle: string;
    contactEmail: string;
    contactPhone?: string;
    contactType: ContactType;
    referralSource?: string;
    customFields: CustomField[];
}

export type BasicDetailsFormData = IndividualCustomerForm | BusinessCustomerForm;
