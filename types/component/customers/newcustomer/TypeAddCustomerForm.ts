import { ContactType, CustomerStage, CustomerType, EntitySize } from '@/lib/graphql/types/generated/graphql';

export interface AddCustomerFormData {
    customerId?: string;
    type: CustomerType;
    stage: CustomerStage;
    name: string;
    email: string;
    phone: string;
    referralSource: string;
    customFields: Array<{
        label: string;
        key: string;
        dataType: string;
        description: string;
        value: string;
    }>;
    companyLegalName: string;
    companyWebsite: string;
    companySize: EntitySize;
    industry: string;
    contactName: string;
    contactTitle: string;
    contactEmail: string;
    contactPhone: string;
    contactType: ContactType;
    notes: Array<{
        content: string;
        tags: string[];
    }>;
    newNotes: {
        content: string;
        tags: string[];
    };
    assignments: {
        accountManager: string;
        supportRepresentative: string;
    };
}