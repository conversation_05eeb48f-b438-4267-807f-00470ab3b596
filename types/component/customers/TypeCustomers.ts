import { CustomersView } from '@/component/customers/CustomersViewEnum';
import { CustomerStatus } from '@/lib/graphql/types/generated/graphql';

export interface SetViewProps {
    setView: (view: CustomersView) => void;
}

export interface AllCustomersTableProps extends SetViewProps { }

export interface CustomersControlsProps {
    searchQuery: string;
    onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onAddCustomer: () => void;
}

export interface CustomersContainerProps {
    children: React.ReactNode;
}

export interface CustomerDetailViewProps extends SetViewProps { } 