export interface BusinessMetric {
    id: string;
    label: string;
    value: number | string;
    icon: React.ReactNode;
    trend?: {
        direction: 'up' | 'down' | 'neutral';
        percentage: number;
        period: string;
    };
    color?: string;
    format?: 'number' | 'currency' | 'percentage';
}

export interface BusinessMetricsProps {
    metrics: BusinessMetric[];
    className?: string;
}

export interface TaskItem {
    id: string;
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    completed: boolean;
    dueDate?: string;
    category: string;
    actionUrl?: string;
}

export interface TaskChecklistProps {
    tasks: TaskItem[];
    onTaskComplete: (taskId: string) => void;
    onTaskClick: (task: TaskItem) => void;
    className?: string;
}

export interface DashboardLayoutProps {
    onboardingComponent?: React.ReactNode;
    metricsComponent?: React.ReactNode;
    aiInsightsComponent?: React.ReactNode;
    tasksComponent?: React.ReactNode;
    className?: string;
}
