import {
    CustomerType,
    CustomerStatus,
    Currency,
    QuoteStatus,
    ProductType
} from '@/lib/graphql/types/generated/graphql';

// Use GraphQL QuoteStatus enum instead of custom enum
export { QuoteStatus as ProposalStatus };

// Interface for customer selection in proposal - matches GraphQL Customer type
export interface ProposalCustomer {
    id: string;
    name: string;
    type: CustomerType;
    status: CustomerStatus;
    email: string;
    phone?: string;
}

// Interface for billing address - now supported in QuoteUpsertInput as addressInput
export interface BillingAddress {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}

// Interface for account assignment - now supported in QuoteUpsertInput as assignments
export interface AccountAssignment {
    salesExecutive: string;
    customerSupportManager: string;
}

// Interface for quote details - now fully matches QuoteUpsertInput dateInput
export interface QuoteDetails {
    description: string;
    dateOfQuoteSending: string; // Maps to dateInput.validFrom
    expiryDate: string; // Maps to dateInput.validTill
}

// Interface for product in proposal - maps to QuoteProductCreateInput
export interface ProposalProduct {
    id: string;
    name: string;
    productCode?: string; // Required by QuoteProductCreateInput
    description?: string; // Optional in QuoteProductCreateInput
    dimensions: Array<{
        key: string;
        value: string;
    }>;
    listPrice: {
        value: number;
        currency: Currency;
    };
    discount?: {
        percentage: number;
        value: number;
        currency: Currency;
    };
    sellingPrice: {
        value: number;
        currency: Currency;
    };
    productUnit: {
        unit: number;
        unitType: string;
    };
    tax?: {
        percentage: number;
        value: number;
        currency: Currency;
    };
    // Additional fields for GraphQL compatibility
    referenceProduct?: {
        type: ProductType;
        id: string;
    };
}

// Main proposal form data interface
export interface ProposalFormData {
    proposalId?: string;

    // Section 1: Customer and Basic Details
    customer: ProposalCustomer | null;
    companyBillingAddress: string;
    customerBillingAddress: string;
    quoteDetails: QuoteDetails;
    accountAssignment: AccountAssignment;

    // Section 2: Products
    products: ProposalProduct[];

    // Calculated totals
    subtotal: {
        value: number;
        currency: Currency;
    };

    totalDiscount: {
        value: number;
        currency: Currency;
    };

    totalTax: {
        value: number;
        currency: Currency;
    };
    total: {
        value: number;
        currency: Currency;
    };

    //Quote currency
    currency: Currency;

    // Metadata
    status: QuoteStatus;
    createdDate: string;
    lastModified: string;
}

// Interface for product selection modal
export interface ProductSelectionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onProductSelect: (product: ProposalProduct) => void;
    selectedProducts: ProposalProduct[];
}

// Interface for master product (from catalog)
export interface MasterProductForProposal {
    id: string;
    name: string;
    productCode?: string; // Required for GraphQL QuoteProductCreateInput
    description?: string; // Optional description
    dimensions: Array<{
        key: string;
        value: string;
    }>;
    listPrice: {
        value: number;
        currency: Currency;
    };
    status: string;
    productUnit: {
        unit: number;
        unitType: string;
    },
}

// Props for proposal controls component
export interface ProposalControlsProps {
    searchQuery: string;
    onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onAddProposal: () => void;
}

// Interface for proposal with basic details (for listing)
export interface ProposalWithBasicDetails {
    id: string;
    proposalNumber: string;
    customerName: string;
    customerType: CustomerType;
    customerStatus: CustomerStatus;
    salesExecutive: string;
    salesExecutiveId: string;
    customerSuccessManager: string;
    customerSuccessManagerId: string;
    totalValue: number;
    currency: string;
    status: QuoteStatus;
    createdDate: string;
    expiryDate: string;
    lastModified: string;
    customerAcceptedDate: string | null;
}
