import { gql } from "@apollo/client";

export const PROPOSAL_PRODUCT_UPSERT_MUTATION = gql`
  mutation ProposalProductUpsert($input: QuoteUpsertInput!) {
    quoteUpsert(input: $input) {  
      id
      status
    }
  }
`;

/*
{  
      id
      status
    
 quoteTotalListPrice {
        value
        currency
      }
      quoteTotalSellingPrice {
        value
        currency     
      }
      quoteTotalTaxAmount {
        value
        currency
      }
      quoteTotalDiscountAmount {
        value   
        currency
      }
      customer {
        id
        ... CustomerBusiness {
          basicDetails {
            legalName
          }
        } 
        ... CustomerIndividual {
          basicDetails {
            contactDetails {
              name
            }  
          }
        }
      } 
}
*/