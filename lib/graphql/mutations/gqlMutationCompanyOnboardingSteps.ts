import { gql } from "@apollo/client";


export const COMPANY_USER_CREATE = gql`
  mutation CompanyUserCreate($input: CompanyUserCreateInput!) {
    companyUserCreate(input: $input) {
      id
      name
      email
      status
      roles
    }
  }
`;


export const COMPANY_USER_INVITE = gql`
  mutation CompanyUserInvite($companyUserId: ID!) {
    companyUserInvite(companyUserId: $companyUserId) {
      id
      name
      email
      status
      roles
    }
  }
`;

export const COMPANY_USER_DEACTIVATE = gql`
  mutation CompanyUserDeactivate($companyUserId: ID!) {
    companyUserDeactivate(companyUserId: $companyUserId) {
      success
      message
    }
  }
`;

export const COMPANY_USER_ACTIVATE = gql`
  mutation CompanyUserActivate {
    companyUserActivate {
      id
      name
      email
      status
      roles
    }
  }
`;

