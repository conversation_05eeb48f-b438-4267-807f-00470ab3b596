import { gql } from "@apollo/client";


export const CUSTOMER_UPSERT_BASIC_DETAILS_MUTATION = gql`
mutation CustomerUpsertBasicDetails($input: CustomerUpsertBasicDetailsInput!) {
    customerUpsertBasicDetails(input: $input) {
        id
    }
  }
`

export const CUSTOMER_UPSERT_ADDITIONAL_DETAILS_MUTATION = gql`
  mutation CustomerUpsertAdditionalDetails($input: CustomerUpsertAdditionalDetailsInput!) {
      customerUpsertAdditionalDetails(input: $input) {
          id
    }
  }
`