import { gql } from "@apollo/client";

export const GET_ALL_CUSTOMERS = gql`
  query GetAllCustomers($status: CustomerStatus, $stage: CustomerStage, $customerType: CustomerType) {
      getCustomers(
        filters: {
          status: $status,
          stage: $stage,
          customerType: $customerType
        }
      ) {
        id
        company {
          id
        }
        status
        stage
        type
        ...on CustomerIndividual {
          basicDetails {
              contactDetails {
                  contactType
                  name
                  title
                  email
                  phoneNo
              }
              referralSource
        }
      }
      ... on CustomerBusiness {
          basicDetails {
              legalName
              website
              size
              industry
              referralSource
              contactDetails {
                  contactType
                  name
                  title
                  email
                  phoneNo
              }
          }
      }
    }
  }
`;

export const GET_CUSTOMER_BY_ID = gql`
  query GetCustomerById($id: ID!) {
    getCustomer(id: $id) {
      id
      status
      stage
      type
      company {
        id
      }
      ... on CustomerIndividual {
        basicDetails {
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
          referralSource
        }
        notes {
          id
          content
          tags
        }
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        customTags {
          id
          key
          label
          value
          type
          description
        }
      }
      ... on CustomerBusiness { 
        basicDetails {
          legalName
          website
          size
          industry
          referralSource
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
        }
        notes {
          id
          content
          tags
        }
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        customTags {
          id
          key
          label
          value
          type
          description
        }
      }
   }
}
`;
