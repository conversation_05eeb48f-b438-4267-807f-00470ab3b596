# GraphQL Setup with Code Generation

This directory contains the GraphQL setup for the OnePlatform frontend, including automatic TypeScript type generation from the GraphQL schema.

## Overview

The GraphQL setup includes:
- **Apollo Client** configuration for Next.js 15 app router
- **GraphQL Code Generator** for automatic TypeScript type generation
- **Type-safe GraphQL operations** with generated React hooks
- **Fragment masking** for better type safety and performance

## Directory Structure

```
lib/graphql/
├── client.ts                 # Apollo Client configuration
├── provider.tsx              # Apollo Provider wrapper for Next.js
├── index.ts                  # Main exports
├── source/
│   └── schema-generated.graphql  # GraphQL schema source
├── queries/                  # GraphQL queries organized by domain
│   ├── admin/
│   └── company/
├── fragments/                # Reusable GraphQL fragments
├── types/
│   └── generated/            # Auto-generated TypeScript types
│       ├── graphql.ts        # Generated types and hooks
│       ├── gql.ts            # Type-safe gql function
│       ├── index.ts          # Generated exports
│       └── fragment-masking.ts # Fragment masking utilities
├── utils/                    # GraphQL utilities
└── examples/                 # Usage examples
```

## Code Generation

### Running Code Generation

```bash
# Generate types once
npm run codegen

# Watch mode for development
npm run codegen:watch

# Alternative commands
npm run graphql:generate
npm run graphql:watch
```

### Configuration

The code generation is configured in `codegen.ts` at the project root:

- **Schema Source**: `lib/graphql/source/schema-generated.graphql`
- **Documents**: All `.ts` and `.tsx` files in `lib/graphql/`, `app/`, and `component/`
- **Output**: `lib/graphql/types/generated/`

### Generated Files

1. **`graphql.ts`**: Contains all TypeScript types and React hooks
2. **`gql.ts`**: Type-safe `gql` function for writing queries
3. **`fragment-masking.ts`**: Utilities for fragment masking
4. **`index.ts`**: Re-exports for easy importing

## Usage Examples

### 1. Using Generated Hooks

```typescript
import { useGetCompanyBasicDetailsQuery } from '@/lib/graphql';

function CompanyProfile() {
  const { data, loading, error } = useGetCompanyBasicDetailsQuery();
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  const company = data?.getCompany;
  
  return (
    <div>
      <h1>{company?.name || 'Unknown Company'}</h1>
      <p>Status: {company?.status}</p>
    </div>
  );
}
```

### 2. Type-Safe Query Writing

```typescript
import { gql } from '@/lib/graphql';

// TypeScript will validate this query against the schema
const GET_CUSTOMER = gql(`
  query GetCustomer($id: ID!) {
    getCustomer(id: $id) {
      id
      type
      status
      stage
    }
  }
`);
```

### 3. Using Generated Types

```typescript
import type { Company, CompanyStatus } from '@/lib/graphql';

function processCompany(company: Company) {
  // TypeScript knows the exact shape of the company object
  console.log(`Company: ${company.name}`);
  
  if (company.status === CompanyStatus.ACTIVE) {
    // Type-safe enum usage
    console.log('Company is active');
  }
}
```

## Best Practices

### 1. Query Organization

- Place queries in domain-specific folders under `queries/`
- Use descriptive query names that match their purpose
- Group related queries in the same file

### 2. Fragment Usage

- Create reusable fragments for common data patterns
- Use fragment masking for better type safety
- Place fragments in the `fragments/` directory

### 3. Type Safety

- Always use the generated hooks instead of raw `useQuery`
- Leverage TypeScript's type checking for query validation
- Use the type-safe `gql` function from the generated code

### 4. Development Workflow

- Run `npm run codegen:watch` during development
- Regenerate types after schema changes
- Commit generated files to version control

## Integration with Apollo Client

The generated types integrate seamlessly with the Apollo Client setup:

```typescript
import { makeClient } from '@/lib/graphql';

// The client is configured with the schema from typeDefs
const client = makeClient();
```

## Environment Variables

- `GRAPHQL_ENDPOINT`: GraphQL server endpoint (default: `http://localhost:8080/graphql`)
- `APOLLO_CLIENT_TIMEOUT`: Request timeout in milliseconds (default: `30000`)

## Troubleshooting

### Common Issues

1. **Generation Fails**: Check that all GraphQL queries are valid against the schema
2. **Type Errors**: Ensure queries match the schema structure exactly
3. **Missing Types**: Run `npm run codegen` to regenerate types

### Schema Updates

When the GraphQL schema changes:
1. Update `lib/graphql/source/schema-generated.graphql`
2. Run `npm run codegen` to regenerate types
3. Update any queries that no longer match the schema
4. Test the application to ensure type safety

## Next Steps

- Add mutations and subscriptions as needed
- Implement error handling patterns
- Add caching strategies for better performance
- Consider implementing optimistic updates for mutations
