/**
 * Clerk Authentication Utilities for Apollo Client
 * 
 * This module provides utilities for integrating Clerk authentication
 * with Apollo Client GraphQL requests, including JWT token management,
 * validation, and refresh scenarios.
 */

import { useAuth } from '@clerk/nextjs';
import { useCallback, useEffect, useState } from 'react';

/**
 * Interface for authentication state
 */
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
  error: string | null;
}

/**
 * Interface for authentication utilities
 */
export interface AuthUtils {
  getToken: () => Promise<string | null>;
  isTokenValid: (token: string | null) => boolean;
  refreshToken: () => Promise<string | null>;
}

/**
 * Custom hook for managing authentication state with Clerk
 * 
 * This hook provides a unified interface for authentication state
 * that integrates seamlessly with Apollo Client.
 */
export function useClerkAuth(): AuthState & AuthUtils {
  const { isSignedIn, isLoaded, getToken: clerkGetToken } = useAuth();
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Get the current JWT token from Clerk
   */
  const getToken = useCallback(async (): Promise<string | null> => {
    try {
      if (!isSignedIn || !isLoaded) {
        return null;
      }

      const token = await clerkGetToken();
      setToken(token);
      setError(null);
      return token;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get authentication token';
      setError(errorMessage);
      setToken(null);
      return null;
    }
  }, [isSignedIn, isLoaded, clerkGetToken]);

  /**
   * Check if a token is valid (not null and not expired)
   */
  const isTokenValid = useCallback((token: string | null): boolean => {
    if (!token) return false;

    try {
      // Parse JWT payload to check expiration
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if token is expired (with 30 second buffer)
      return payload.exp && payload.exp > currentTime + 30;
    } catch {
      return false;
    }
  }, []);

  /**
   * Refresh the authentication token
   */
  const refreshToken = useCallback(async (): Promise<string | null> => {
    try {
      if (!isSignedIn || !isLoaded) {
        return null;
      }

      // Force token refresh by passing { skipCache: true }
      const newToken = await clerkGetToken({ skipCache: true });
      setToken(newToken);
      setError(null);
      return newToken;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh authentication token';
      setError(errorMessage);
      setToken(null);
      return null;
    }
  }, [isSignedIn, isLoaded, clerkGetToken]);

  // Initialize token on mount and when auth state changes
  useEffect(() => {
    if (isLoaded && isSignedIn) {
      getToken();
    } else if (isLoaded && !isSignedIn) {
      setToken(null);
      setError(null);
    }
  }, [isLoaded, isSignedIn, getToken]);

  return {
    isAuthenticated: isSignedIn ?? false,
    isLoading: !isLoaded,
    token,
    error,
    getToken,
    isTokenValid,
    refreshToken,
  };
}

/**
 * Server-side utility to get token from Clerk
 * 
 * This function can be used in server components or API routes
 * to get the authentication token.
 */
export async function getServerToken(): Promise<string | null> {
  try {
    // This will be used in server-side scenarios
    // For now, return null as we're focusing on client-side integration
    return null;
  } catch {
    return null;
  }
}

/**
 * Utility function to format authorization header
 */
export function formatAuthHeader(token: string | null): Record<string, string> {
  if (!token) {
    return {};
  }

  return {
    Authorization: `Bearer ${token}`,
  };
}

/**
 * Type guard to check if an error is an authentication error
 */
export function isAuthError(error: any): boolean {
  if (!error) return false;

  // Check for common authentication error patterns
  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.extensions?.code || error.code || '';

  return (
    errorCode === 'UNAUTHENTICATED' ||
    errorCode === 'UNAUTHORIZED' ||
    errorCode === 'FORBIDDEN' ||
    errorMessage.includes('unauthorized') ||
    errorMessage.includes('unauthenticated') ||
    errorMessage.includes('token') ||
    error.networkError?.statusCode === 401 ||
    error.networkError?.statusCode === 403
  );
}

/**
 * Authentication configuration for Apollo Client
 */
export interface AuthConfig {
  tokenRefreshThreshold: number; // seconds before expiry to refresh
  maxRetries: number; // max retry attempts for failed requests
  retryDelay: number; // delay between retries in milliseconds
}

/**
 * Default authentication configuration
 */
export const defaultAuthConfig: AuthConfig = {
  tokenRefreshThreshold: 600, // 5 minutes
  maxRetries: 3,
  retryDelay: 1000, // 1 second
};
