import { ApolloError } from '@apollo/client';
import { isAuthError } from './auth';

/**
 * Custom error type for GraphQL operations
 */
export interface GraphQLErrorInfo {
    message: string;
    code?: string;
    details?: string;
}

/**
 * Formats an Apollo error into a user-friendly error object
 */
export function formatGraphQLError(error: ApolloError): GraphQLErrorInfo {
    // Handle network errors
    if (error.networkError) {
        return {
            message: 'Unable to connect to the server. Please check your connection.',
            code: 'NETWORK_ERROR',
            details: error.networkError.message,
        };
    }

    // Handle GraphQL errors
    if (error.graphQLErrors?.length) {
        const firstError = error.graphQLErrors[0];
        return {
            message: firstError.message || 'An error occurred while processing your request.',
            code: firstError.extensions?.code as string,
            details: JSON.stringify(firstError.extensions),
        };
    }

    // Handle unknown errors
    return {
        message: error.message || 'An unexpected error occurred.',
        code: 'UNKNOWN_ERROR',
    };
}

/**
 * Checks if the error is a specific type of error (e.g., authentication error)
 * @deprecated Use isAuthError from './auth' instead for more comprehensive auth error detection
 */
export function isAuthenticationError(error: ApolloError): boolean {
    return isAuthError(error);
}

/**
 * Gets a user-friendly error message based on the error type
 */
export function getUserFriendlyErrorMessage(error: ApolloError): string {
    const formattedError = formatGraphQLError(error);

    // Check if this is an authentication error using the enhanced detection
    if (isAuthError(error)) {
        return 'Your session has expired. Please sign in again.';
    }

    switch (formattedError.code) {
        case 'FORBIDDEN':
            return 'You do not have permission to perform this action.';
        case 'NETWORK_ERROR':
            return 'Unable to connect to the server. Please check your connection.';
        case 'BAD_REQUEST':
            return 'Invalid request. Please check your input and try again.';
        case 'INTERNAL_ERROR':
            return 'An internal server error occurred. Please try again later.';
        case 'NOT_FOUND':
            return 'The requested resource was not found.';
        case 'RATE_LIMITED':
            return 'Too many requests. Please wait a moment and try again.';
        default:
            return formattedError.message;
    }
}

/**
 * Enhanced error categorization for better user experience
 */
export interface ErrorCategory {
    type: 'auth' | 'network' | 'validation' | 'permission' | 'server' | 'unknown';
    severity: 'low' | 'medium' | 'high' | 'critical';
    retryable: boolean;
    userMessage: string;
    technicalMessage: string;
}

/**
 * Categorizes an Apollo error for better handling and user experience
 */
export function categorizeError(error: ApolloError): ErrorCategory {
    const formattedError = formatGraphQLError(error);

    // Authentication errors
    if (isAuthError(error)) {
        return {
            type: 'auth',
            severity: 'high',
            retryable: false,
            userMessage: 'Your session has expired. Please sign in again.',
            technicalMessage: formattedError.message,
        };
    }

    // Network errors
    if (error.networkError) {
        return {
            type: 'network',
            severity: 'medium',
            retryable: true,
            userMessage: 'Unable to connect to the server. Please check your connection.',
            technicalMessage: formattedError.details || formattedError.message,
        };
    }

    // Permission errors
    if (formattedError.code === 'FORBIDDEN') {
        return {
            type: 'permission',
            severity: 'medium',
            retryable: false,
            userMessage: 'You do not have permission to perform this action.',
            technicalMessage: formattedError.message,
        };
    }

    // Validation errors
    if (formattedError.code === 'BAD_REQUEST' || formattedError.code === 'VALIDATION_ERROR') {
        return {
            type: 'validation',
            severity: 'low',
            retryable: false,
            userMessage: 'Please check your input and try again.',
            technicalMessage: formattedError.message,
        };
    }

    // Server errors
    if (formattedError.code === 'INTERNAL_ERROR' || formattedError.code === 'INTERNAL_SERVER_ERROR') {
        return {
            type: 'server',
            severity: 'high',
            retryable: true,
            userMessage: 'An internal server error occurred. Please try again later.',
            technicalMessage: formattedError.message,
        };
    }

    // Unknown errors
    return {
        type: 'unknown',
        severity: 'medium',
        retryable: false,
        userMessage: formattedError.message || 'An unexpected error occurred.',
        technicalMessage: formattedError.message,
    };
}