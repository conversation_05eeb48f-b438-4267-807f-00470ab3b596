import { ApolloError } from '@apollo/client';

/**
 * Type guard to check if an error is a company not found error
 */
export function isCompanyNotFound(error: ApolloError): boolean {
  if (!error) return false;

  const errorCode = error.graphQLErrors[0]?.extensions?.errorCode || '';
  return errorCode === 'COMPANY_NOT_FOUND_FOR_USER' || errorCode === 'COMPANY_NOT_FOUND_FOR_COMPANY_ID' || errorCode === 'COMPANY_IS_INACTIVE';
}