/**
 * Apollo Client Configuration
 *
 * This file sets up the Apollo Client instance with proper configuration
 * for Next.js 15 app router, including SSR support, caching, error handling,
 * and Clerk authentication integration.
 */

import { ApolloClient, HttpLink, InMemoryCache, from, type ApolloLink } from '@apollo/client';
import typeDefs from './source/schema-generated.graphql';
import { createAuthLinks, type AuthLinkDeps } from './links/authLink';

// Environment variables
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT || 'http://localhost:8080/graphql';
const TIMEOUT = parseInt(process.env.APOLLO_CLIENT_TIMEOUT || '30000', 10);

/**
 * Create Apollo Client instance with optional authentication
 */
export function makeClient(authDeps?: AuthLinkDeps) {
  const httpLink = new HttpLink({
    uri: GRAPHQL_ENDPOINT,
    credentials: 'same-origin',
    fetchOptions: {
      timeout: TIMEOUT,
    },
  });

  // Create the link chain
  let link: ApolloLink = httpLink;

  // Add authentication links if auth dependencies are provided
  if (authDeps) {
    const authLinks = createAuthLinks(authDeps);
    link = from([...authLinks, httpLink]);
  }

  return new ApolloClient({
    cache: new InMemoryCache({
      // Configure cache policies for better authentication handling
      typePolicies: {
        Query: {
          fields: {
            // Add any specific field policies here if needed
          },
        },
      },
    }),
    link,
    typeDefs: typeDefs,
    connectToDevTools: process.env.NODE_ENV === 'development',
    // Default options for all queries/mutations
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all',
        notifyOnNetworkStatusChange: true,
      },
      query: {
        errorPolicy: 'all',
      },
      mutate: {
        errorPolicy: 'all',
      },
    },
  });
}

/**
 * Create Apollo Client instance without authentication (for server-side or public use)
 */
export function makePublicClient() {
  return makeClient();
}
