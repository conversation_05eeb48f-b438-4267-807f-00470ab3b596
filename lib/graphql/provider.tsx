/**
 * Apollo Provider Wrapper with Authentication
 *
 * This file provides the Apollo Client provider for Next.js app router.
 * It handles both client-side and server-side rendering scenarios,
 * and integrates Clerk authentication with Apollo Client.
 */

'use client';

import { ReactNode, useMemo, useCallback } from 'react';
import { ApolloProvider } from '@apollo/client';
import { useRouter } from 'next/navigation';
import { makeClient, makePublicClient } from './client';
import { useClerkAuth } from './utils/auth';

interface ApolloWrapperProps {
  children: ReactNode;
}

/**
 * Apollo Provider Wrapper Component with Authentication
 *
 * This component wraps the application with Apollo Client provider
 * and handles authentication integration with Clerk.
 */
export function ApolloWrapper({ children }: ApolloWrapperProps) {
  const router = useRouter();
  const auth = useClerkAuth();

  // Handle authentication errors by redirecting to sign-in
  const handleAuthError = useCallback(() => {
    console.warn('Authentication error detected, redirecting to sign-in');
    router.push('/sign-in');
  }, [router]);

  // Create Apollo Client with authentication dependencies
  const client = useMemo(() => {
    if (auth.isLoading) {
      // Return a basic client while auth is loading
      return makePublicClient();
    }

    if (auth.isAuthenticated) {
      // Create authenticated client
      return makeClient({
        getToken: auth.getToken,
        refreshToken: auth.refreshToken,
        isTokenValid: auth.isTokenValid,
        onAuthError: handleAuthError,
      });
    } else {
      // Create public client for unauthenticated users
      return makePublicClient();
    }
  }, [
    auth.isLoading,
    auth.isAuthenticated,
    auth.getToken,
    auth.refreshToken,
    auth.isTokenValid,
    handleAuthError,
  ]);

  return (
    <ApolloProvider client={client}>
      {children}
    </ApolloProvider>
  );
}
