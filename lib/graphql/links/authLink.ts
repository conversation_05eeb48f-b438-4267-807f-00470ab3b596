/**
 * Apollo Client Authentication Link
 * 
 * This module provides an Apollo Link that automatically adds JWT tokens
 * from Clerk authentication to all GraphQL requests as Authorization headers.
 * It handles token refresh scenarios and authentication errors.
 */

import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { fromPromise } from '@apollo/client/link/utils';
import { formatAuthHeader, isAuthError, defaultAuthConfig, type AuthConfig } from '../utils/auth';

/**
 * Interface for authentication link dependencies
 */
export interface AuthLinkDeps {
  getToken: () => Promise<string | null>;
  refreshToken: () => Promise<string | null>;
  isTokenValid: (token: string | null) => boolean;
  onAuthError?: () => void;
}

/**
 * Create an authentication context link that adds JWT tokens to requests
 * 
 * This link automatically adds the Authorization header with the JWT token
 * to all GraphQL requests. It handles token validation and refresh scenarios.
 */
export function createAuthLink(deps: AuthLinkDeps, config: AuthConfig = defaultAuthConfig) {
  const { getToken, refreshToken, isTokenValid, onAuthError } = deps;

  return setContext(async (_, { headers }) => {
    try {
      // Get the current token
      let token = await getToken();

      // If token is invalid or expired, try to refresh it
      if (!isTokenValid(token)) {
        token = await refreshToken();
      }

      // Return headers with authorization
      return {
        headers: {
          ...headers,
          ...formatAuthHeader(token),
        },
      };
    } catch (error) {
      console.warn('Failed to set authentication context:', error);
      
      // Return headers without authorization on error
      return {
        headers: {
          ...headers,
        },
      };
    }
  });
}

/**
 * Create an error link that handles authentication errors
 * 
 * This link intercepts GraphQL errors and handles authentication-related
 * errors by attempting to refresh tokens and retry requests.
 */
export function createAuthErrorLink(deps: AuthLinkDeps, config: AuthConfig = defaultAuthConfig) {
  const { refreshToken, onAuthError } = deps;
  let isRefreshing = false;
  let failedQueue: Array<{ resolve: Function; reject: Function }> = [];

  const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });
    
    failedQueue = [];
  };

  return onError(({ graphQLErrors, networkError, operation, forward }) => {
    // Check if this is an authentication error
    const hasAuthError = graphQLErrors?.some(isAuthError) || 
                        (networkError && isAuthError(networkError));

    if (hasAuthError) {
      // If we're already refreshing, queue this request
      if (isRefreshing) {
        return fromPromise(
          new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          })
        ).flatMap(() => {
          return forward(operation);
        });
      }

      isRefreshing = true;

      return fromPromise(
        refreshToken()
          .then((newToken) => {
            if (newToken) {
              // Update the operation context with the new token
              operation.setContext(({ headers = {} }) => ({
                headers: {
                  ...headers,
                  ...formatAuthHeader(newToken),
                },
              }));

              processQueue(null, newToken);
              return newToken;
            } else {
              throw new Error('Failed to refresh token');
            }
          })
          .catch((error) => {
            processQueue(error, null);
            
            // Call the auth error handler if provided
            if (onAuthError) {
              onAuthError();
            }
            
            throw error;
          })
          .finally(() => {
            isRefreshing = false;
          })
      ).flatMap(() => {
        return forward(operation);
      });
    }

    // For non-auth errors, just forward them
    return;
  });
}

/**
 * Create a combined authentication link that includes both context and error handling
 * 
 * This is a convenience function that creates both the auth context link
 * and the auth error link, returning them as an array for easy use in Apollo Client.
 */
export function createAuthLinks(deps: AuthLinkDeps, config: AuthConfig = defaultAuthConfig) {
  return [
    createAuthErrorLink(deps, config),
    createAuthLink(deps, config),
  ];
}

/**
 * Type for the authentication link configuration
 */
export interface AuthLinkConfig extends AuthConfig {
  deps: AuthLinkDeps;
}

/**
 * Factory function to create authentication links with configuration
 */
export function createConfiguredAuthLinks(config: AuthLinkConfig) {
  const { deps, ...authConfig } = config;
  return createAuthLinks(deps, authConfig);
}
