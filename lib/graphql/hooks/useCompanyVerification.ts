/**
 * Company Verification Hook
 * 
 * This hook handles post-authentication company verification by executing
 * the GetCompany query and managing the flow for users without companies.
 */

import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { isCompanyNotFound } from '@/lib/graphql/utils/companyNotFound';
import { useGetCompanyQuery } from '../types/generated/hooks';

export interface CompanyVerificationState {
  /** Whether the user has a valid company */
  hasCompany: boolean;
  /** Whether the verification check is currently loading */
  isLoading: boolean;
  /** Whether the user is authenticated */
  isAuthenticated: boolean;
  /** Any error that occurred during verification */
  error: string | null;
  /** The company data if available */
  company: any | null;
  /** Function to retry the verification check */
  retry: () => void;
}

/**
 * Hook for managing post-authentication company verification
 * 
 * This hook automatically executes the GetCompany query after authentication
 * and handles the UnauthenticatedException by redirecting to company creation.
 */
export function useCompanyVerification(): CompanyVerificationState {
  const { isSignedIn, isLoaded } = useAuth();
  const router = useRouter();
  const [hasCompany, setHasCompany] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Execute GetCompany query only when user is authenticated
  const {
    data,
    loading,
    error: queryError,
    refetch
  } = useGetCompanyQuery({
    skip: !isSignedIn || !isLoaded,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
  });

  // Handle query results and errors
  useEffect(() => {
    if (!isSignedIn || !isLoaded) {
      setHasCompany(false);
      setError(null);
      return;
    }

    if (loading) {
      setError(null);
      return;
    }

    if (queryError) {
      if (isCompanyNotFound(queryError)) {
        setHasCompany(false);
        setError('No company found - redirecting to company creation');

        // Redirect to company creation route
        router.push('/company/create');
        return;
      }

      setError(queryError.message || 'Failed to verify company');
      setHasCompany(false);
      return;
    }

    // Success case - user has a company
    if (data?.getCompany) {
      setHasCompany(true);
      setError(null);
    } else {
      setHasCompany(false);
      setError('No company found');
      router.push('/company/create');
    }
  }, [data, loading, queryError, isSignedIn, isLoaded, router]);

  // Retry function
  const retry = useCallback(() => {
    setError(null);
    refetch();
  }, [refetch]);

  return {
    hasCompany,
    isLoading: loading || !isLoaded,
    isAuthenticated: isSignedIn ?? false,
    error,
    company: data?.getCompany || null,
    retry,
  };
}

/**
 * Hook for checking if the current route should be protected by company verification
 *
 * This hook determines whether the current route requires company verification
 * based on the pathname.
 */
export function useRequiresCompanyVerification(): boolean {
  // Routes that don't require company verification
  const publicRoutes = [
    '/sign-in',
    '/sign-up',
    '/company/create', // Company creation route
  ];

  // Check if current route is public
  if (typeof window !== 'undefined') {
    const pathname = window.location.pathname;
    return !publicRoutes.some(route => pathname.startsWith(route));
  }

  return true; // Default to requiring verification on server-side
}
