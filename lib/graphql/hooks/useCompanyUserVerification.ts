/**
 * Company User Verification Hook
 * 
 * This hook handles post-authentication company user verification by executing
 * the GetCompanyUsers query and managing the flow for users who are not active
 */

import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { CompanyUserStatus } from '@/lib/graphql/types/generated/graphql';
import { useGetCompanyUserQuery } from '@/lib/graphql/types/generated/hooks';

export interface CompanyUserVerificationState {
    /** Whether the user is an active company user */
    isActiveUser: boolean;
    /** Whether the user is invited but not activated */
    isInvitedUser: boolean;
    /** Whether the verification check is currently loading */
    isLoading: boolean;
    /** Whether the user is authenticated */
    isAuthenticated: boolean;
    /** Any error that occurred during verification */
    error: string | null;
    /** The company user data if available */
    companyUser: any | null;
    /** Function to retry the verification check */
    retry: () => void;
}

/**
 * Hook for managing post-authentication company user verification
 * 
 * This hook automatically executes the GetCompanyUsers query after authentication
 * and verifies if the current user is active or invited.
 */
export function useCompanyUserVerification(): CompanyUserVerificationState {
    const { isSignedIn, isLoaded, userId, signOut } = useAuth();
    const router = useRouter();
    const [isActiveUser, setIsActiveUser] = useState(false);
    const [isInvitedUser, setIsInvitedUser] = useState(false);
    const [error, setError] = useState<string | null>(null);
    // Execute GetCompanyUsers query only when user is authenticated
    const {
        data,
        loading,
        error: queryError,
        refetch
    } = useGetCompanyUserQuery({
        skip: !isSignedIn || !isLoaded,
        errorPolicy: 'all',
        notifyOnNetworkStatusChange: true,
    });

    // Handle query results and errors
    useEffect(() => {
        if (!isSignedIn || !isLoaded || !userId) {
            setIsActiveUser(false);
            setIsInvitedUser(false);
            setError(null);
            return;
        }

        if (loading) {
            setError(null);
            return;
        }

        if (queryError) {
            setError(queryError.message || 'Failed to verify user status');
            setIsActiveUser(false);
            setIsInvitedUser(false);
            return;
        }

        const companyUser = data?.getCompanyUser;

        if (companyUser) {
            setIsActiveUser(companyUser.status === CompanyUserStatus.Active);
            setIsInvitedUser(companyUser.status === CompanyUserStatus.Invited);
            setError(null);

            // Redirect invited users to activation
            if (companyUser.status === CompanyUserStatus.Invited) {
                router.push('/company/user/activate?redirectUrl=' + window.location.pathname);
            }
        } else {
            setIsActiveUser(false);
            setIsInvitedUser(false);
            setError('User not found in any company, please contact support');
            signOut();
        }
    }, [data, loading, queryError, isSignedIn, isLoaded, router]);

    // Retry function
    const retry = useCallback(() => {
        setError(null);
        refetch();
    }, [refetch]);

    return {
        isActiveUser,
        isInvitedUser,
        isLoading: loading || !isLoaded,
        isAuthenticated: isSignedIn ?? false,
        error,
        companyUser: data?.getCompanyUser || null,
        retry,
    };
}

/**
 * Hook for checking if the current route should be protected by company user verification
 *
 * This hook determines whether the current route requires company user verification
 * based on the pathname.
 */
export function useRequiresCompanyUserVerification(): boolean {
    // Routes that don't require company user verification
    const publicRoutes = [
        '/sign-in',
        '/sign-up',
        '/company/create',
        '/company/user/activate'
    ];

    // Check if current route is public
    if (typeof window !== 'undefined') {
        const pathname = window.location.pathname;
        return !publicRoutes.some(route => pathname.startsWith(route));
    }

    return true; // Default to requiring verification on server-side
}
