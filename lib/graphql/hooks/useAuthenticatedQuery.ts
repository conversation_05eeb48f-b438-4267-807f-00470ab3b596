/**
 * Authentication-aware GraphQL hooks
 * 
 * This module provides custom hooks that integrate Clerk authentication
 * with Apollo Client GraphQL operations, providing better error handling
 * and authentication state management.
 */

import { useQuery, useMutation, useSubscription, type QueryHookOptions, type MutationHookOptions, type SubscriptionHookOptions, type OperationVariables, type ApolloError } from '@apollo/client';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { categorizeError, type ErrorCategory } from '../utils/errorHandling';
import type { DocumentNode } from 'graphql';

/**
 * Enhanced query options with authentication handling
 */
export interface AuthenticatedQueryOptions<TData = any, TVariables extends OperationVariables = OperationVariables> extends Omit<QueryHookOptions<TData, TVariables>, 'onError'> {
  requireAuth?: boolean;
  onAuthError?: () => void;
  onError?: (error: ErrorCategory) => void;
}

/**
 * Enhanced mutation options with authentication handling
 */
export interface AuthenticatedMutationOptions<TData = any, TVariables extends OperationVariables = OperationVariables> extends Omit<MutationHookOptions<TData, TVariables>, 'onError'> {
  requireAuth?: boolean;
  onAuthError?: () => void;
  onError?: (error: ErrorCategory) => void;
}

/**
 * Enhanced subscription options with authentication handling
 */
export interface AuthenticatedSubscriptionOptions<TData = any, TVariables extends OperationVariables = OperationVariables> extends Omit<SubscriptionHookOptions<TData, TVariables>, 'onError'> {
  requireAuth?: boolean;
  onAuthError?: () => void;
  onError?: (error: ErrorCategory) => void;
}

/**
 * Custom hook for authenticated GraphQL queries
 * 
 * This hook wraps Apollo's useQuery with authentication-aware error handling
 * and automatic redirection for authentication errors.
 */
export function useAuthenticatedQuery<TData = any, TVariables extends OperationVariables = OperationVariables>(
  query: DocumentNode,
  options: AuthenticatedQueryOptions<TData, TVariables> = {}
) {
  const { isSignedIn, isLoaded } = useAuth();
  const router = useRouter();

  const {
    requireAuth = true,
    onAuthError,
    onError: customOnError,
    ...apolloOptions
  } = options;

  // Handle authentication errors
  const handleAuthError = useCallback(() => {
    if (onAuthError) {
      onAuthError();
    } else {
      router.push('/sign-in');
    }
  }, [onAuthError, router]);

  // Enhanced error handling
  const handleError = useCallback((error: ApolloError) => {
    const categorizedError = categorizeError(error);

    if (categorizedError.type === 'auth') {
      handleAuthError();
    }

    if (customOnError) {
      customOnError(categorizedError);
    }
  }, [handleAuthError, customOnError]);

  // Skip query if authentication is required but user is not authenticated
  const shouldSkip = requireAuth && (!isLoaded || !isSignedIn);

  const result = useQuery<TData, TVariables>(query, {
    ...apolloOptions,
    skip: shouldSkip || apolloOptions.skip,
    onError: handleError,
  });

  return {
    ...result,
    isAuthLoading: !isLoaded,
    isAuthenticated: isSignedIn,
    authError: requireAuth && isLoaded && !isSignedIn ? 'Authentication required' : null,
  };
}

/**
 * Custom hook for authenticated GraphQL mutations
 * 
 * This hook wraps Apollo's useMutation with authentication-aware error handling.
 */
export function useAuthenticatedMutation<TData = any, TVariables extends OperationVariables = OperationVariables>(
  mutation: DocumentNode,
  options: AuthenticatedMutationOptions<TData, TVariables> = {}
) {
  const { isSignedIn, isLoaded } = useAuth();
  const router = useRouter();

  const {
    requireAuth = true,
    onAuthError,
    onError: customOnError,
    ...apolloOptions
  } = options;

  // Handle authentication errors
  const handleAuthError = useCallback(() => {
    if (onAuthError) {
      onAuthError();
    } else {
      router.push('/sign-in');
    }
  }, [onAuthError, router]);

  // Enhanced error handling
  const handleError = useCallback((error: ApolloError) => {
    const categorizedError = categorizeError(error);

    if (categorizedError.type === 'auth') {
      handleAuthError();
    }

    if (customOnError) {
      customOnError(categorizedError);
    }
  }, [handleAuthError, customOnError]);

  const [mutateFunction, result] = useMutation<TData, TVariables>(mutation, {
    ...apolloOptions,
    onError: handleError,
  });

  // Wrapper function that checks authentication before executing
  const authenticatedMutate = useCallback(async (options?: any) => {
    if (requireAuth && (!isLoaded || !isSignedIn)) {
      const authError = new Error('Authentication required');
      if (customOnError) {
        customOnError(categorizeError({ message: authError.message } as ApolloError));
      }
      throw authError;
    }

    return mutateFunction(options);
  }, [mutateFunction, requireAuth, isLoaded, isSignedIn, customOnError]);

  return [
    authenticatedMutate,
    {
      ...result,
      isAuthLoading: !isLoaded,
      isAuthenticated: isSignedIn,
      authError: requireAuth && isLoaded && !isSignedIn ? 'Authentication required' : null,
    }
  ] as const;
}

/**
 * Custom hook for authenticated GraphQL subscriptions
 * 
 * This hook wraps Apollo's useSubscription with authentication-aware error handling.
 */
export function useAuthenticatedSubscription<TData = any, TVariables extends OperationVariables = OperationVariables>(
  subscription: DocumentNode,
  options: AuthenticatedSubscriptionOptions<TData, TVariables> = {}
) {
  const { isSignedIn, isLoaded } = useAuth();
  const router = useRouter();

  const {
    requireAuth = true,
    onAuthError,
    onError: customOnError,
    ...apolloOptions
  } = options;

  // Handle authentication errors
  const handleAuthError = useCallback(() => {
    if (onAuthError) {
      onAuthError();
    } else {
      router.push('/sign-in');
    }
  }, [onAuthError, router]);

  // Enhanced error handling
  const handleError = useCallback((error: ApolloError) => {
    const categorizedError = categorizeError(error);

    if (categorizedError.type === 'auth') {
      handleAuthError();
    }

    if (customOnError) {
      customOnError(categorizedError);
    }
  }, [handleAuthError, customOnError]);

  // Skip subscription if authentication is required but user is not authenticated
  const shouldSkip = requireAuth && (!isLoaded || !isSignedIn);

  const result = useSubscription<TData, TVariables>(subscription, {
    ...apolloOptions,
    skip: shouldSkip || apolloOptions.skip,
    onError: handleError,
  });

  return {
    ...result,
    isAuthLoading: !isLoaded,
    isAuthenticated: isSignedIn,
    authError: requireAuth && isLoaded && !isSignedIn ? 'Authentication required' : null,
  };
}

/**
 * Hook for managing authentication state and GraphQL client integration
 */
export function useAuthenticationStatus() {
  const { isSignedIn, isLoaded, getToken } = useAuth();
  const router = useRouter();

  const signOut = useCallback(() => {
    router.push('/sign-in');
  }, [router]);

  const requireAuthentication = useCallback(() => {
    if (!isLoaded || !isSignedIn) {
      router.push('/sign-in');
      return false;
    }
    return true;
  }, [isLoaded, isSignedIn, router]);

  return {
    isAuthenticated: isSignedIn ?? false,
    isLoading: !isLoaded,
    getToken,
    signOut,
    requireAuthentication,
  };
}
