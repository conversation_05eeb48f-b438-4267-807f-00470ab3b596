/**
 * Core Apollo Client exports with Authentication
 *
 * This file exports the essential Apollo Client functionality:
 * - The Apollo Client instance with authentication
 * - The Apollo Provider wrapper for Next.js with Clerk integration
 * - Generated GraphQL types and hooks
 * - Type-safe gql function
 * - Authentication utilities and hooks
 */

export { ApolloWrapper } from './provider';
export { makeClient } from './client';

// Export generated GraphQL types and hooks
export * from './types/generated/graphql';
export { gql } from './types/generated';

// Export utility types for better developer experience
export type {
    Maybe,
    Exact,
    MakeOptional,
    MakeMaybe,
    Scalars,
} from './types/generated/graphql';

// Export authentication utilities
export { useClerkAuth, formatAuthHeader, isAuthError, defaultAuthConfig } from './utils/auth';
export type { AuthState, AuthUtils, AuthConfig } from './utils/auth';

// Export authentication links
export { createAuthLink, createAuthErrorLink, createAuthLinks } from './links/authLink';
export type { AuthLinkDeps, AuthLinkConfig } from './links/authLink';

// Export enhanced error handling
export { formatGraphQLError, getUserFriendlyErrorMessage, categorizeError, isAuthenticationError } from './utils/errorHandling';
export type { GraphQLErrorInfo, ErrorCategory } from './utils/errorHandling';

// Export authentication-aware hooks
export { useAuthenticatedQuery, useAuthenticatedMutation, useAuthenticatedSubscription, useAuthenticationStatus } from './hooks/useAuthenticatedQuery';
export type { AuthenticatedQueryOptions, AuthenticatedMutationOptions, AuthenticatedSubscriptionOptions } from './hooks/useAuthenticatedQuery';
