module.exports = {
    content: [
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
        './component/**/*.{js,ts,jsx,tsx,mdx}',
        './app/**/*.{js,ts,jsx,tsx,mdx}',
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
        extend: {
            colors: {
                'primary-blue': '#45C9F7',
                'secondary-orange': '#F77345'
            },
            fontFamily: {
                heading: ['Poppins', 'sans-serif'],
                body: ['Poppins', 'sans-serif'],
            },
            keyframes: {
                slideIn: {
                    '0%': { transform: 'translateY(-10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' }
                }
            },
            animation: {
                slideIn: 'slideIn 0.2s ease-out'
            }
        },
    },
    plugins: [],
} 