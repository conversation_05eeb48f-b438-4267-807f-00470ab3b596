"use client";
import { useEffect } from "react";
import { useApolloClient } from "@apollo/client";
import { useRouter } from "next/navigation";

export default function SignOutRedirect() {
    const apolloClient = useApolloClient();
    const router = useRouter();

    useEffect(() => {
        async function cleanupAndRedirect() {
            try {
                await apolloClient.clearStore();
                localStorage.clear();
                sessionStorage.clear();
                // TODO: Clear any custom tokens if you store them elsewhere
            } catch (error) {
                console.error("Error during sign-out cleanup:", error);
            } finally {
                router.replace("/");
            }
        }
        cleanupAndRedirect();
    }, [apolloClient, router]);

    return (
        <div className="flex flex-col items-center justify-center min-h-screen">
            <span className="text-lg font-medium">Signing you out...</span>
        </div>
    );
} 