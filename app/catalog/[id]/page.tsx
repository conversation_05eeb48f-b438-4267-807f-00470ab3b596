'use client';
import { ArrowLeft, Edit } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

// Mock data for a single service - in a real app, you would fetch this by ID
const service = {
    id: 'SRV-001',
    name: 'Dental Cleaning',
    category: 'General Dentistry',
    description: 'A routine procedure to remove plaque and tartar from teeth, promoting oral hygiene and preventing cavities and gum disease. Recommended every 6 months.',
    status: 'Active',
    taxable: true,
    hsnCode: '998311',
    glAccount: '4100 - Service Revenue',
    pricingModel: 'simple',
    pricingType: 'flat',
    price: 99.00,
    currency: 'USD',
};

const ServiceDetailPage = () => {
    const { id } = useParams<{ id: string }>();
    const router = useRouter();

    const DetailItem = ({ label, value, isBadge = false }: { label: string; value: string | number | React.ReactNode; isBadge?: boolean }) => (
        <div>
            <dt className="text-sm font-medium text-gray-500">{label}</dt>
            {isBadge ? value : <dd className="mt-1 text-sm text-gray-900">{value}</dd>}
        </div>
    );

    return (
        <div className="min-h-screen">
            <header className="mt-6">
                <div className="max-w-3xl mx-auto">
                    <div className="flex items-center justify-between h-16">
                        <button
                            onClick={() => router.push('/services')}
                            className="flex items-center text-gray-600 hover:text-secondary transition-colors h-10 px-4 rounded-md"
                        >
                            <ArrowLeft className="h-5 w-5 mr-2 -ml-3" />
                            <span>Back to Services</span>
                        </button>
                        <button
                            type="button"
                            className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-white hover:bg-primary-hover h-10 px-4"
                        >
                            <Edit className="-ml-1 mr-2 h-4 w-4" />
                            <span>Edit Service</span>
                        </button>
                    </div>
                </div>
            </header>

            <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="max-w-3xl mx-auto">
                    {/* Page Header */}
                    <div className="md:flex md:items-center md:justify-between mb-8">
                        <div className="min-w-0 flex-1">
                            <h1 className="text-3xl font-bold leading-7 text-gray-900 sm:truncate sm:tracking-tight">
                                {service.name}
                            </h1>
                        </div>
                        <div className="mt-4 flex md:ml-4 md:mt-0">
                            <span
                                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${service.status === 'Active'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                    }`}
                            >
                                {service.status}
                            </span>
                        </div>
                    </div>

                    {/* Service Details Card */}
                    <div className="bg-white p-8 rounded-lg">
                        <dl className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2">
                            <div className="sm:col-span-2">
                                <DetailItem label="Description" value={service.description} />
                            </div>
                            <DetailItem label="Service ID" value={service.id} />
                            <DetailItem label="Category" value={service.category} />

                            <div className="sm:col-span-2 pt-6 border-t border-gray-200">
                                <h3 className="text-lg font-medium text-gray-900">Pricing</h3>
                            </div>
                            <DetailItem label="Pricing Model" value={`${service.pricingModel.charAt(0).toUpperCase() + service.pricingModel.slice(1)} Pricing`} />
                            <DetailItem label="Pricing Type" value={`${service.pricingType.charAt(0).toUpperCase() + service.pricingType.slice(1)} Pricing`} />
                            <DetailItem label="Price" value={`$${service.price.toFixed(2)} ${service.currency}`} />

                            <div className="sm:col-span-2 pt-6 border-t border-gray-200">
                                <h3 className="text-lg font-medium text-gray-900">Accounting</h3>
                            </div>
                            <DetailItem label="Tax Information" value={service.taxable ? 'Taxable' : 'Tax Exempt'} />
                            <DetailItem label="HSN Code" value={service.hsnCode} />
                            <DetailItem label="GL Account" value={service.glAccount} />
                        </dl>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default ServiceDetailPage; 