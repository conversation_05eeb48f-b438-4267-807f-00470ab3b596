'use client';

import React from 'react';

import ComponentCompanyUserSetting from '@/component/company/settings/ComponentCompanyUserSetting';
import ComponentCompanySettingsLayout from '@/component/company/settings/ComponentCompanySettingsLayout';
import ComponentBasicDetailsSetting from '@/component/company/settings/ComponentBasicDetailsSetting';

const CompanySettingsPage: React.FC = () => {
    return (
        <ComponentCompanySettingsLayout>
            <ComponentBasicDetailsSetting editMode={false} initialCreate={false} />
        </ComponentCompanySettingsLayout>
    );
};

export default CompanySettingsPage;
