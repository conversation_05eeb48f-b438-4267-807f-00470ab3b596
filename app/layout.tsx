import type { Metadata } from "next";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignedIn,
  useClerk,
} from "@clerk/nextjs";
import "./globals.css";
import Navbar from "@/component/navbar/ComponentNavbar";
import { DM_Sans } from "next/font/google";
import { ApolloWrapper } from "@/lib/graphql/provider";
import { ComponentRouteProtection } from "@/component/auth/ComponentRouteProtection";

const dmsans = DM_Sans({
  subsets: ['latin'],
  // Optimize for dashboard/analytics use
  weight: ['400', '500', '600', '700'],
  // Enable slightly enhanced kerning
  variable: '--font-dm-sans',
  display: 'swap',
})

export const metadata: Metadata = {
  title: "OnePlatform",
  description: "Everything you need to manage your business in one place",
};

function LayoutContent({ children }: { children: React.ReactNode }) {
  return (
    <SignedIn>
      <ComponentRouteProtection>
        <header>
          <Navbar />
        </header>
        <main>
          {children}
        </main>
      </ComponentRouteProtection>
    </SignedIn>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider afterSignOutUrl={'/signout/redirect'}>
      <html lang="en" className={dmsans.variable}>
        <body className="min-h-screen bg-gradient-primary-to-secondary">
          <ApolloWrapper>
            <LayoutContent>
              {children}
            </LayoutContent>
          </ApolloWrapper>
        </body>
      </html>
    </ClerkProvider>
  );
}
