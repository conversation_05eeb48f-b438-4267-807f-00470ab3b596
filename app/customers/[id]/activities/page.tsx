'use client';

import React from 'react';
import ComponentCustomerDetailLayout from '@/component/customers/customerdetails/ComponentCustomerDetailLayout';
import ComponentCustomerActivitiesTimeline from '@/component/customers/customerdetails/ComponentCustomerActivitiesTimeline';

// Mock data - in a real app, this would be fetched based on the customer ID
const mockActivities = [
    { date: '2024-03-01', description: '<PERSON> rendered Dental Cleaning Service for 3 hours', amount: '600 USD' },
    { date: '2024-02-15', description: '<PERSON> <PERSON> rendered Alignment (2 sessions)', amount: '100 USD' },
    { date: '2024-02-10', description: 'Initial consultation completed', amount: '150 USD' },
    { date: '2024-02-05', description: 'X-Ray imaging performed', amount: '75 USD' },
    { date: '2024-01-20', description: 'Follow-up appointment scheduled', amount: '0 USD' },
];

const CustomerActivitiesPage = () => {
    // In a real app, you would fetch activities based on the customer ID
    // const params = useParams();
    // const customerId = params.id as string;
    // const { data: activities, isLoading, error } = useCustomerActivities(customerId);

    return (
        <ComponentCustomerDetailLayout>
            <div className="">
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Customer Activities</h2>
                    <p className="text-gray-600">Timeline of all customer interactions and activities</p>
                </div>
                <div className=''>
                    <ComponentCustomerActivitiesTimeline activities={mockActivities} />
                </div>
            </div>
        </ComponentCustomerDetailLayout>
    );
};

export default CustomerActivitiesPage;
