'use client';

import React from 'react';
import ComponentCustomerDetailLayout from '@/component/customers/customerdetails/ComponentCustomerDetailLayout';
import ComponentCustomerBasicDetails from '@/component/customers/customerdetails/ComponentCustomerBasicDetails';
import ComponentCustomerNotes from '@/component/customers/customerdetails/ComponentCustomerNotes';
import ComponentCustomerAssignments from '@/component/customers/customerdetails/ComponentCustomerAssignments';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';

const mockInsights = {
    lifetimeValue: 12000,
    avgInvoice: 400,
    totalVisits: 30,
};

const CustomerOverviewPage = () => {
    const params = useParams();
    const customerId = params.id as string;
    const router = useRouter();

    return (
        <div className="max-w-6xl mx-auto mb-10">
                {/* Header Section */}
      
        
            <ComponentCustomerDetailLayout>
                <h2 className="text-xl font-bold text-gray-900 mb-6 mt-20">Customer Information</h2>
                <div className="space-y-8">
                    {/* Section 1: Basic Customer Information */}
                    <div className="bg-white rounded-lg border border-gray-200 p-6">
                        <ComponentCustomerBasicDetails customerId={customerId} />
                    </div>

                    <h2 className="text-xl font-bold text-gray-900 mb-6">Customer Insights</h2>

                {/* Section 2: Customer Insights Carousel */}
                <div className="">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-blue-900">ARR</h3>
                                <div className="text-2xl font-bold text-blue-600">${mockInsights.lifetimeValue.toLocaleString()}</div>
                            </div>
                            <p className="text-blue-700 text-sm">Annual Recurring Revenue</p>
                        </div>

                        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-green-900">Total Products</h3>
                                <div className="text-2xl font-bold text-green-600">8</div>
                            </div>
                            <p className="text-green-700 text-sm">Active product subscriptions</p>
                        </div>

                        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-purple-900">Number of Quotes</h3>
                                <div className="text-2xl font-bold text-purple-600">{mockInsights.totalVisits}</div>
                            </div>
                            <p className="text-purple-700 text-sm">Total quotes generated</p>
                        </div>
                    </div>
                </div>

                    <h2 className="text-xl font-bold text-gray-900 mb-6">Additional Details</h2>

                {/* Section 3: Additional Details Management */}
                <div className="">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <ComponentCustomerNotes customerId={customerId} />
                        <ComponentCustomerAssignments customerId={customerId} />
                        {/* Future: Add documents and custom tags sections here */}
                    </div>
                </div>
            </div>
            </ComponentCustomerDetailLayout>
        </div>
    );
};

export default CustomerOverviewPage;