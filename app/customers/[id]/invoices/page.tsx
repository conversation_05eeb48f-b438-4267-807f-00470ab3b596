'use client';

import React from 'react';
import ComponentCustomerDetailLayout from '@/component/customers/customerdetails/ComponentCustomerDetailLayout';
import ComponentCustomerInvoices from '@/component/customers/customerdetails/ComponentCustomerInvoices';

// Mock data - in a real app, this would be fetched based on the customer ID
const mockInvoices = [
    { id: 'INV-001', date: '2024-01-10', amount: 500, status: 'Paid' },
    { id: 'INV-002', date: '2024-02-05', amount: 350, status: 'Pending' },
    { id: 'INV-003', date: '2024-02-15', amount: 750, status: 'Paid' },
    { id: 'INV-004', date: '2024-03-01', amount: 420, status: 'Pending' },
];

const CustomerInvoicesPage = () => {
    // In a real app, you would fetch invoices based on the customer ID
    // const params = useParams();
    // const customerId = params.id as string;
    // const { data: invoices, isLoading, error } = useCustomerInvoices(customerId);

    return (
        <ComponentCustomerDetailLayout>
            <div className="">
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Customer Invoices</h2>
                    <p className="text-gray-600">View and manage all invoices for this customer</p>
                </div>
                <div className=''>
                    <ComponentCustomerInvoices invoices={mockInvoices} />
                </div>
            </div>
        </ComponentCustomerDetailLayout>
    );
};

export default CustomerInvoicesPage;
