'use client';

import React from 'react';
import ComponentCustomerDetailLayout from '@/component/customers/customerdetails/ComponentCustomerDetailLayout';
import ComponentCustomerServices from '@/component/customers/customerdetails/ComponentCustomerServices';

// Mock data - in a real app, this would be fetched based on the customer ID
const mockServices = [
    { name: 'Dental Cleaning', description: 'Professional dental cleaning service', cost: '200 USD per Hour' },
    { name: 'Alignment', description: '1-5 sessions', cost: '50 USD each' },
    { name: 'Consultation', description: 'Initial consultation and assessment', cost: '150 USD per session' },
    { name: 'X-Ray Imaging', description: 'Digital X-ray imaging service', cost: '75 USD per image' },
];

const CustomerServicesPage = () => {
    return (
        <ComponentCustomerDetailLayout>
            <div className="">
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Customer Services</h2>
                    <p className="text-gray-600">Services provided to this customer</p>
                </div>
                <div className=''>
                    <ComponentCustomerServices services={mockServices} />
                </div>
            </div>
        </ComponentCustomerDetailLayout>
    );
};

export default CustomerServicesPage;
