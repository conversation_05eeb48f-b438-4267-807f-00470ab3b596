/* =========================
   Card Shuffle Peer Switch
   ========================= */

.card-shuffle-peer-switch {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.card-shuffle-peer-input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
}

.card-shuffle-peer-slider {
    width: 44px;
    height: 24px;
    background: var(--color-bg, #fff);
    border-radius: var(--radius-full, 9999px);
    border: 1px solid var(--color-border, #d1d5db);
    position: relative;
    transition: background var(--transition-fast, 0.2s), border-color var(--transition-fast, 0.2s);
}

.card-shuffle-peer-input:checked+.card-shuffle-peer-slider {
    background: var(--color-primary, #2563eb);
    border-color: var(--color-primary, #2563eb);
}

.card-shuffle-peer-slider::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: var(--color-bg, #fff);
    border-radius: var(--radius-full, 9999px);
    border: 1px solid var(--color-border, #d1d5db);
    transition: transform var(--transition-fast, 0.2s), border-color var(--transition-fast, 0.2s);
}

.card-shuffle-peer-input:checked+.card-shuffle-peer-slider::after {
    transform: translateX(20px);
    border-color: var(--color-bg, #fff);
}