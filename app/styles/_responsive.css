@media (min-width: 768px) {
    .section-padding {
        padding: 8rem 1rem;
    }

    .md\:py-6 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }

    .md\:py-32 {
        padding-top: 8rem;
        padding-bottom: 8rem;
    }

    .md\:py-40 {
        padding-top: 10rem;
        padding-bottom: 10rem;
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .md\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .md\:gap-12 {
        gap: 3rem;
    }

    .md\:gap-16 {
        gap: 4rem;
    }
}