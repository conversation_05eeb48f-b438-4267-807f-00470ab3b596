/* Background Colors */
.hover\:bg-black:hover {
    background-color: #000;
}

.hover\:bg-gray:hover {
    background-color: #f3f4f6;
}

.hover\:bg-secondary:hover {
    background-color: var(--secondary-orange);
}

.hover\:bg-primary:hover {
    background-color: var(--primary-blue);
}

/** Scaling, Opacity, shadow, radius **/
.hover\:bg-opacity-90:hover {
    opacity: 0.9;
}

.hover\:scale-105:hover {
    transform: scale(1.05);
}

.hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.2);
}

.hover\:rounded-full:hover {
    border-radius: 9999px;
}

/* Text Colors */
.hover\:text-white:hover {
    color: #fff;
}

.hover\:text-secondary:hover {
    color: var(--secondary-orange);
}