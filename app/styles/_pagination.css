/* Pagination Styles */

.pagination-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid var(--color-border, #e5e7eb);
    background: var(--color-bg, #fff);
    color: var(--color-text, #374151);
    font-size: 0.875rem;
    font-weight: 500;
    transition: background 0.2s, color 0.2s, border 0.2s;
    cursor: pointer;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--color-hover, #f3f4f6);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: var(--color-primary, #2563eb);
    color: #fff;
    border-color: var(--color-primary, #2563eb);
}

.pagination-btn.nav {
    background: var(--color-primary, #2563eb);
    color: #fff;
    border: none;
}