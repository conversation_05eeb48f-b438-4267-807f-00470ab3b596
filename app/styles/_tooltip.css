/* Tooltip Component Styles */

.tooltip-container {
    position: relative;
    display: inline-block;
}

.tooltip {
    position: absolute;
    z-index: 1000;
    padding: 0.5rem 0.75rem;
    background-color: var(--gray);
    color: var(--white);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    transform: translateY(0.25rem);
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    font-family: var(--font-body);
}

.tooltip.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Position variants */
.tooltip.position-top {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(0.25rem);
    margin-bottom: 0.5rem;
}

.tooltip.position-top.visible {
    transform: translateX(-50%) translateY(0);
}

.tooltip.position-bottom {
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-0.25rem);
    margin-top: 0.5rem;
}

.tooltip.position-bottom.visible {
    transform: translateX(-50%) translateY(0);
}

.tooltip.position-left {
    right: 100%;
    top: 50%;
    transform: translateY(-50%) translateX(0.25rem);
    margin-right: 0.5rem;
}

.tooltip.position-left.visible {
    transform: translateY(-50%) translateX(0);
}

.tooltip.position-right {
    left: 100%;
    top: 50%;
    transform: translateY(-50%) translateX(-0.25rem);
    margin-left: 0.5rem;
}

.tooltip.position-right.visible {
    transform: translateY(-50%) translateX(0);
}

/* Arrow styles */
.tooltip::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

.tooltip.position-top::before {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0.375rem 0.375rem 0 0.375rem;
    border-color: var(--gray) transparent transparent transparent;
}

.tooltip.position-bottom::before {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 0.375rem 0.375rem 0.375rem;
    border-color: transparent transparent var(--gray) transparent;
}

.tooltip.position-left::before {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-width: 0.375rem 0 0.375rem 0.375rem;
    border-color: transparent transparent transparent var(--gray);
}

.tooltip.position-right::before {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-width: 0.375rem 0.375rem 0.375rem 0;
    border-color: transparent var(--gray) transparent transparent;
}

/* Responsive behavior */
@media (max-width: 768px) {
    .tooltip {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
        max-width: 200px;
        white-space: normal;
        text-align: center;
    }
    
    .tooltip::before {
        border-width: 0.25rem;
    }
    
    .tooltip.position-top::before {
        border-width: 0.25rem 0.25rem 0 0.25rem;
    }
    
    .tooltip.position-bottom::before {
        border-width: 0 0.25rem 0.25rem 0.25rem;
    }
    
    .tooltip.position-left::before {
        border-width: 0.25rem 0 0.25rem 0.25rem;
    }
    
    .tooltip.position-right::before {
        border-width: 0.25rem 0.25rem 0.25rem 0;
    }
}

/* Accessibility - Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .tooltip {
        transition: opacity 0.1s ease-in-out;
        transform: none;
    }
    
    .tooltip.position-top,
    .tooltip.position-bottom,
    .tooltip.position-left,
    .tooltip.position-right {
        transform: none;
    }
    
    .tooltip.position-top.visible,
    .tooltip.position-bottom.visible,
    .tooltip.position-left.visible,
    .tooltip.position-right.visible {
        transform: none;
    }
}
