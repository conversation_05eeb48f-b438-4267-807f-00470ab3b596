/* Default dropdown override using theme colors and backgrounds */

.dropdown-default {
    background-color: var(--background);
    color: var(--gray);
    border: 1px solid var(--primary-blue);
    border-radius: 0.5rem;
    /* matches .rounded-lg */
    transition: box-shadow 0.2s, border-color 0.2s;
    outline: none;
    box-shadow: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' fill='none' stroke='%233B5BA5' stroke-width='2' viewBox='0 0 24 24'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1.25em;
    padding-right: 2.5rem;
}

.dropdown-default:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 2px var(--primary-blue);
}

.dropdown-default:hover {
    border-color: var(--secondary-orange);
}

.dropdown-default:disabled {
    background-color: var(--white-40-opacity);
    color: var(--gray);
    opacity: 0.7;
    cursor: not-allowed;
}

/* Option styling for dropdown values */
.dropdown-default option {
    background-color: var(--background);
    color: var(--gray);
}