@keyframes infinite-scroll {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(-50%);
    }
}

.animate-infinite-scroll {
    animation: infinite-scroll 5s linear infinite;
}

.animate-infinite-scroll:hover {
    animation-play-state: paused;
}

.animate-infinite-scroll>* {
    transition: transform 0.1s ease;
}

@keyframes infinite-scroll-left {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(-50%);
    }
}

@keyframes infinite-scroll-right {
    from {
        transform: translateX(-50%);
    }

    to {
        transform: translateX(0);
    }
}

.animate-infinite-scroll-left {
    animation: infinite-scroll-left 5s linear infinite;
}

.animate-infinite-scroll-right {
    animation: infinite-scroll-right 5s linear infinite;
}

.animate-infinite-scroll-left:hover,
.animate-infinite-scroll-right:hover {
    animation-play-state: paused;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(40px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }

    to {
        opacity: 0;
        transform: translateY(40px);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(0);
        opacity: 1;
    }

    to {
        transform: translateY(100%);
        opacity: 0;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-fadeOut {
    animation: fadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slideUp {
    animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slideDown {
    animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}