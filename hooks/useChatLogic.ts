import { useState, useCallback, useEffect } from 'react';
import { ChatMessage } from '@/types/component/ai/TypeAIInsightsModal';
import { getMockAIResponse } from '@/utils/mockAI';

export const useChatLogic = (initialMessage?: string) => {
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [isTyping, setIsTyping] = useState(false);

    const addUserMessage = useCallback((text: string) => {
        if (!text.trim()) return;

        const userMessage: ChatMessage = {
            id: crypto.randomUUID(),
            sender: 'user',
            text,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        };
        setMessages(prev => [...prev, userMessage]);
        setIsTyping(true);
    }, []);

    useEffect(() => {
        if (messages.length > 0 && messages[messages.length - 1].sender === 'user') {
            const lastMessage = messages[messages.length - 1];
            getMockAIResponse(lastMessage.text).then(aiText => {
                const aiMessage: ChatMessage = {
                    id: crypto.randomUUID(),
                    sender: 'bot',
                    text: aiText,
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                };
                setMessages(prev => [...prev, aiMessage]);
                setIsTyping(false);
            });
        }
    }, [messages]);

    useEffect(() => {
        if (initialMessage) {
            addUserMessage(initialMessage);
        }
    }, [initialMessage, addUserMessage]);


    return { messages, isTyping, addUserMessage };
}; 