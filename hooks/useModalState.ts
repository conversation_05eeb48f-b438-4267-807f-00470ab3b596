import { useState, useCallback, useEffect } from 'react';

export const useModalState = (initialOpen = false) => {
    const [isOpen, setIsOpen] = useState(initialOpen);

    const openModal = useCallback(() => setIsOpen(true), []);
    const closeModal = useCallback(() => setIsOpen(false), []);

    useEffect(() => {
        const handleEsc = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                closeModal();
            }
        };
        window.addEventListener('keydown', handleEsc);

        return () => {
            window.removeEventListener('keydown', handleEsc);
        };
    }, [closeModal]);

    return { isOpen, openModal, closeModal };
}; 