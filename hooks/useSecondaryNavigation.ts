'use client';

import { useState } from 'react';

export const useSecondaryNavigation = (defaultActiveId?: string) => {
    const [activeSection, setActiveSection] = useState(defaultActiveId || '');

    const handleSectionChange = (sectionId: string) => {
        setActiveSection(sectionId);
    };

    return {
        activeSection,
        setActiveSection: handleSectionChange,
    };
};

export default useSecondaryNavigation;
