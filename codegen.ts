/**
 * GraphQL Code Generator Configuration
 * 
 * This file configures the GraphQL Code Generator to automatically generate
 * TypeScript types from GraphQL schema and operations.
 */

import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  // GraphQL schema source - using local schema file from typeDefs in Apollo Client
  schema: 'lib/graphql/source/schema-generated.graphql',
  overwrite: true,
  // Documents pattern - where to find GraphQL operations (queries, mutations, subscriptions)
  documents: [
    'lib/graphql/**/*.{ts,tsx}',
    '!lib/graphql/source/**/*',
    '!lib/graphql/types/generated/**/*',
  ],

  // Output configuration
  generates: {
    'lib/graphql/types/generated/': {
      preset: 'client',
      plugins: [],
      presetConfig: {
        gqlTagName: 'gql',
        fragmentMasking: {
          unmaskFunctionName: 'getFragmentData',
        }
      }
    },
    'lib/graphql/types/generated/hooks.ts': {
      plugins: ['typescript', 'typescript-operations', 'typescript-react-apollo'],
      config: {
        withHooks: true,
        withComponent: false,
        withHOC: false,
        withRefetchFn: true,
        apolloReactHooksImportFrom: '@apollo/client',
        scalars: {
          DateTime: 'string',
          Date: 'string',
          JSON: 'Record<string, any>',
          Upload: 'File',
          ID: 'string',
          Boolean: 'boolean',
          Int: 'number',
          Float: 'number',
          String: 'string',
          UUID: 'string',
          Email: 'string',
          URL: 'string',
        }
      }
    }
  },

  config: {
    dedupeOperationSuffix: true,
    strict: true,
    immutableTypes: true,
    exactOptionalPropertyTypes: true,
  },

  // Hooks configuration
  hooks: {
    afterAllFileWrite: ['prettier --write'],
  },

  // Watch mode configuration (for development)
  watch: process.env.NODE_ENV === 'development',

  // Error handling
  silent: false,
  errorsOnly: false,
};

export default config;
